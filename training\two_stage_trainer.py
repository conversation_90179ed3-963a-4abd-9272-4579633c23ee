"""
两阶段训练管理器
协调模仿学习和强化学习的训练流程

@authors: Based on original training framework
@organization: GRASP Lab, University of Pennsylvania
@brief: 两阶段训练：第一阶段模仿学习，第二阶段强化学习微调
"""

import os
import sys
import torch
import numpy as np
import time
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
from os.path import join as opj

# 导入现有的训练模块
from train import TRAINER
from td3_algorithm import TD3Agent, ReplayBuffer
from rl_env_wrapper import FlightmareEnvWrapper
from model_adapter import PretrainedModelAdapter

# 添加模型路径
sys.path.append(opj(os.path.dirname(os.path.abspath(__file__)), '../models'))
import model as model_library

class TwoStageTrainer:
    """
    两阶段训练管理器

    第一阶段：模仿学习训练
    第二阶段：强化学习微调
    """

    def __init__(self, args):
        """
        初始化两阶段训练器

        Args:
            args: 训练参数
        """
        self.args = args
        self.device = args.device

        # 创建工作区
        self._setup_workspace()

        # 阶段标志
        self.stage1_completed = False
        self.stage2_completed = False

        # 模型保存路径
        self.stage1_model_path = None
        self.stage2_model_path = None

        print(f"[TwoStageTrainer] 初始化完成，工作区: {self.workspace}")

    def _setup_workspace(self):
        """设置工作区"""
        expname = datetime.now().strftime('two_stage_d%m_%d_t%H_%M')
        self.workspace = opj(self.args.basedir, self.args.logdir, expname)

        wkspc_ctr = 2
        while os.path.exists(self.workspace):
            self.workspace = opj(self.args.basedir, self.args.logdir, expname + f'_{str(wkspc_ctr)}')
            wkspc_ctr += 1

        os.makedirs(self.workspace)

        # 创建子目录
        self.stage1_dir = opj(self.workspace, 'stage1_imitation')
        self.stage2_dir = opj(self.workspace, 'stage2_reinforcement')
        os.makedirs(self.stage1_dir)
        os.makedirs(self.stage2_dir)

        # 初始化日志
        self.writer = SummaryWriter(self.workspace)
        self.logfile = open(opj(self.workspace, 'two_stage_log.txt'), 'w')

        # 保存配置
        with open(opj(self.workspace, 'args.txt'), 'w') as f:
            for arg in sorted(vars(self.args)):
                attr = getattr(self.args, arg)
                f.write(f'{arg} = {attr}\n')

    def mylogger(self, msg):
        """日志记录"""
        print(msg)
        self.logfile.write(msg + '\n')
        self.logfile.flush()

    def train_stage1_imitation_learning(self):
        """
        第一阶段：模仿学习训练
        """
        self.mylogger("[STAGE 1] 开始模仿学习训练...")
        stage1_start = time.time()

        # 修改args以适应第一阶段
        stage1_args = self._prepare_stage1_args()

        # 创建模仿学习训练器
        imitation_trainer = TRAINER(stage1_args)

        # 开始训练
        imitation_trainer.train()

        # 保存最佳模型
        self.stage1_model_path = self._find_best_stage1_model(imitation_trainer.workspace)

        stage1_time = time.time() - stage1_start
        self.mylogger(f"[STAGE 1] 模仿学习训练完成，耗时: {stage1_time:.2f}s")
        self.mylogger(f"[STAGE 1] 最佳模型保存在: {self.stage1_model_path}")

        self.stage1_completed = True

        # 记录到TensorBoard
        self.writer.add_scalar('stage1/training_time', stage1_time, 0)

        return self.stage1_model_path

    def train_stage2_reinforcement_learning(self, rl_episodes=1000, eval_freq=100):
        """
        第二阶段：强化学习微调

        Args:
            rl_episodes: 强化学习训练轮数
            eval_freq: 评估频率
        """
        if not self.stage1_completed:
            raise ValueError("必须先完成第一阶段训练")

        self.mylogger("[STAGE 2] 开始强化学习微调...")
        stage2_start = time.time()

        # 加载预训练模型
        pretrained_model = self._load_pretrained_model()

        # 创建环境
        env = FlightmareEnvWrapper(
            config_path=opj(self.args.basedir, 'flightmare/flightpy/configs/vision/config.yaml')
        )

        # 创建TD3智能体
        agent = TD3Agent(
            state_dim=517,  # 根据实际观测维度调整
            action_dim=3,
            max_action=1.0,
            lr_actor=1e-5,  # 较小的学习率用于微调
            lr_critic=1e-4,
            device=self.device,
            pretrained_model=pretrained_model,
            model_type=self.args.model_type,
            freeze_backbone=False  # 可以根据需要调整
        )

        # 创建经验回放缓冲区
        replay_buffer = ReplayBuffer(capacity=100000)

        # 训练参数
        batch_size = 256
        start_timesteps = 1000  # 随机探索步数
        eval_episodes = 10

        # 训练循环
        total_timesteps = 0
        episode_rewards = []

        for episode in range(rl_episodes):
            episode_start = time.time()

            # 重置环境
            state = env.reset()
            episode_reward = 0
            episode_timesteps = 0
            done = False

            while not done:
                total_timesteps += 1
                episode_timesteps += 1

                # 选择动作
                if total_timesteps < start_timesteps:
                    # 随机探索
                    action = np.random.uniform(-1, 1, size=3)
                else:
                    # 使用策略选择动作
                    action = agent.select_action(state, noise=0.1)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 存储经验
                # 注意：这里需要将state转换为适合存储的格式
                state_np = self._state_to_numpy(state)
                next_state_np = self._state_to_numpy(next_state)

                replay_buffer.push(state_np, action, reward, next_state_np, float(done))

                state = next_state
                episode_reward += reward

                # 训练智能体
                if total_timesteps >= start_timesteps and len(replay_buffer) > batch_size:
                    agent.train(replay_buffer, batch_size)

            episode_rewards.append(episode_reward)
            episode_time = time.time() - episode_start

            # 记录训练信息
            self.mylogger(f"[STAGE 2] Episode {episode+1}/{rl_episodes}, "
                         f"Reward: {episode_reward:.2f}, "
                         f"Steps: {episode_timesteps}, "
                         f"Time: {episode_time:.2f}s")

            # 记录到TensorBoard
            self.writer.add_scalar('stage2/episode_reward', episode_reward, episode)
            self.writer.add_scalar('stage2/episode_length', episode_timesteps, episode)

            # 定期评估
            if (episode + 1) % eval_freq == 0:
                avg_reward = self._evaluate_agent(agent, env, eval_episodes)
                self.mylogger(f"[STAGE 2] 评估结果 (Episode {episode+1}): 平均奖励 = {avg_reward:.2f}")
                self.writer.add_scalar('stage2/eval_reward', avg_reward, episode)

                # 保存模型
                model_path = opj(self.stage2_dir, f'td3_model_episode_{episode+1}.pth')
                agent.save(model_path)

        # 保存最终模型
        self.stage2_model_path = opj(self.stage2_dir, 'td3_final_model.pth')
        agent.save(self.stage2_model_path)

        stage2_time = time.time() - stage2_start
        self.mylogger(f"[STAGE 2] 强化学习训练完成，耗时: {stage2_time:.2f}s")
        self.mylogger(f"[STAGE 2] 最终模型保存在: {self.stage2_model_path}")

        self.stage2_completed = True

        # 记录到TensorBoard
        self.writer.add_scalar('stage2/training_time', stage2_time, 0)
        self.writer.add_scalar('stage2/final_avg_reward', np.mean(episode_rewards[-100:]), 0)

        env.close()
        return self.stage2_model_path

    def _prepare_stage1_args(self):
        """准备第一阶段的参数"""
        stage1_args = self.args
        # 修改日志目录到stage1子目录
        stage1_args.logdir = self.stage1_dir
        return stage1_args

    def _find_best_stage1_model(self, workspace):
        """找到第一阶段的最佳模型"""
        # 简单实现：返回最后保存的模型
        model_files = [f for f in os.listdir(workspace) if f.startswith('model_') and f.endswith('.pth')]
        if model_files:
            latest_model = sorted(model_files)[-1]
            return opj(workspace, latest_model)
        else:
            raise FileNotFoundError("未找到第一阶段训练的模型文件")

    def _load_pretrained_model(self):
        """加载预训练模型"""
        self.mylogger(f"[STAGE 2] 加载预训练模型: {self.stage1_model_path}")

        # 使用模型适配器加载预训练模型
        adapter = PretrainedModelAdapter(self.args.model_type)
        model = adapter.load_pretrained_model(self.stage1_model_path, self.device)

        return model

    def _state_to_numpy(self, state):
        """将状态转换为numpy格式以便存储"""
        if isinstance(state, list):
            # ViT输入格式，需要展平
            img = state[0].cpu().numpy().flatten()
            vel = state[1].cpu().numpy()
            quat = state[2].cpu().numpy()
            return np.concatenate([img, vel, quat])
        else:
            return state

    def _evaluate_agent(self, agent, env, num_episodes=10):
        """评估智能体性能"""
        total_rewards = []

        for _ in range(num_episodes):
            state = env.reset()
            episode_reward = 0
            done = False

            while not done:
                action = agent.select_action(state, noise=0)  # 无噪声评估
                state, reward, done, _ = env.step(action)
                episode_reward += reward

            total_rewards.append(episode_reward)

        return np.mean(total_rewards)

    def run_full_training(self, rl_episodes=1000):
        """
        运行完整的两阶段训练

        Args:
            rl_episodes: 强化学习训练轮数
        """
        self.mylogger("[TwoStageTrainer] 开始完整的两阶段训练流程")

        # 第一阶段：模仿学习
        stage1_model = self.train_stage1_imitation_learning()

        # 第二阶段：强化学习
        stage2_model = self.train_stage2_reinforcement_learning(rl_episodes)

        self.mylogger("[TwoStageTrainer] 两阶段训练完成！")
        self.mylogger(f"模仿学习模型: {stage1_model}")
        self.mylogger(f"强化学习模型: {stage2_model}")

        # 关闭日志
        self.writer.close()
        self.logfile.close()

        return stage1_model, stage2_model
