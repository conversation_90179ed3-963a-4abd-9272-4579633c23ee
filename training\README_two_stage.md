# 两阶段训练系统使用指南

本系统实现了无人机避障任务的两阶段训练流程：
1. **第一阶段**：模仿学习训练ViT模型
2. **第二阶段**：使用TD3强化学习算法进行微调

## 系统架构

### 核心组件

1. **TD3算法实现** (`td3_algorithm.py`)
   - `TD3Agent`: 主要的强化学习智能体
   - `Actor`: 策略网络，支持从预训练ViT模型初始化
   - `Critic`: 价值网络，双Critic架构
   - `ReplayBuffer`: 经验回放缓冲区

2. **环境包装器** (`rl_env_wrapper.py`)
   - `FlightmareEnvWrapper`: 将Flightmare的VisionEnv封装为标准RL环境
   - 支持标准的`reset()`和`step()`接口
   - 实现了针对避障任务的奖励函数

3. **两阶段训练管理器** (`two_stage_trainer.py`)
   - `TwoStageTrainer`: 协调两个阶段的训练流程
   - 自动处理模型加载和保存
   - 提供完整的训练日志和监控

4. **主训练脚本** (`train_two_stage.py`)
   - 支持灵活的训练模式配置
   - 完整的参数解析和配置管理

## 快速开始

### 1. 环境准备

确保已安装以下依赖：
```bash
# PyTorch
pip install torch torchvision

# 其他依赖
pip install numpy opencv-python tensorboard configargparse

# Flightmare (需要单独编译)
# 请参考Flightmare官方文档进行安装
```

### 2. 数据准备

确保训练数据位于正确的目录：
```
training/datasets/data/
├── trajectory_001/
│   ├── data.csv
│   ├── 000001.png
│   ├── 000002.png
│   └── ...
├── trajectory_002/
└── ...
```

### 3. 运行测试

首先运行测试脚本验证系统功能：
```bash
cd training
python test_two_stage.py
```

### 4. 开始训练

#### 完整两阶段训练
```bash
python train_two_stage.py --config config/two_stage_config.txt
```

#### 仅运行模仿学习（第一阶段）
```bash
python train_two_stage.py --config config/two_stage_config.txt --training_mode stage1
```

#### 仅运行强化学习（第二阶段）
```bash
python train_two_stage.py --config config/two_stage_config.txt --training_mode stage2 --stage1_checkpoint path/to/pretrained/model.pth
```

## 配置参数

### 主要配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `training_mode` | 训练模式：'stage1', 'stage2', 'both' | 'both' |
| `model_type` | 模型类型：'ViT', 'ViTLSTM' | 'ViT' |
| `stage1_epochs` | 模仿学习训练轮数 | 100 |
| `stage2_episodes` | 强化学习训练轮数 | 1000 |
| `stage2_lr_actor` | Actor学习率 | 1e-5 |
| `stage2_lr_critic` | Critic学习率 | 1e-4 |

### 奖励函数参数

奖励函数包含以下组件：
- **速度跟踪奖励**: 鼓励跟踪目标速度
- **碰撞惩罚**: 避免与障碍物碰撞
- **平滑性奖励**: 鼓励平滑的控制动作
- **生存奖励**: 基础的生存奖励
- **前进奖励**: 鼓励向前飞行

## 输出结果

### 训练日志

训练过程会在以下位置生成日志：
```
training/logs/two_stage_d[date]_t[time]/
├── stage1_imitation/          # 第一阶段日志
├── stage2_reinforcement/      # 第二阶段日志
├── two_stage_log.txt         # 总体训练日志
└── runs/                     # TensorBoard日志
```

### 模型保存

- **第一阶段模型**: `stage1_imitation/model_*.pth`
- **第二阶段模型**: `stage2_reinforcement/td3_*.pth`

### TensorBoard监控

启动TensorBoard查看训练过程：
```bash
tensorboard --logdir training/logs/
```

监控指标包括：
- 第一阶段：训练损失、验证损失
- 第二阶段：回合奖励、评估性能、训练损失

## 高级使用

### 自定义奖励函数

修改`rl_env_wrapper.py`中的`_compute_reward`方法：
```python
def _compute_reward(self, quad_state, action, is_collision):
    # 自定义奖励逻辑
    reward = ...
    return reward
```

### 自定义网络结构

修改`td3_algorithm.py`中的`Actor`和`Critic`类：
```python
class Actor(nn.Module):
    def __init__(self, ...):
        # 自定义网络结构
        pass
```

### 调整训练参数

创建自定义配置文件：
```bash
cp config/two_stage_config.txt config/my_config.txt
# 编辑 my_config.txt
python train_two_stage.py --config config/my_config.txt
```

## 故障排除

### 常见问题

1. **Flightmare环境未找到**
   - 确保Flightmare已正确编译和安装
   - 检查Python路径设置

2. **CUDA内存不足**
   - 减小batch_size
   - 使用CPU训练（设置device=cpu）

3. **训练不收敛**
   - 调整学习率
   - 检查奖励函数设计
   - 增加训练轮数

### 调试模式

启用详细日志：
```bash
python train_two_stage.py --config config/two_stage_config.txt --verbose
```

## 性能优化

### 训练加速

1. 使用多GPU训练
2. 调整batch_size和buffer_size
3. 使用混合精度训练

### 内存优化

1. 减小replay buffer大小
2. 使用梯度累积
3. 定期清理缓存

## 扩展功能

### 支持其他算法

系统设计支持扩展其他强化学习算法：
- SAC (Soft Actor-Critic)
- PPO (Proximal Policy Optimization)
- DDPG (Deep Deterministic Policy Gradient)

### 支持其他环境

可以轻松适配其他仿真环境：
1. 继承`FlightmareEnvWrapper`基类
2. 实现必要的接口方法
3. 调整观测和动作空间

## 贡献指南

欢迎贡献代码和改进建议！请遵循以下步骤：
1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目基于原始ViT避障项目的许可证。
