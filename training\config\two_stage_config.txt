# 两阶段训练配置文件
# 第一阶段：模仿学习 + 第二阶段：强化学习微调

# 基础配置
device = cuda
basedir = /home/<USER>/code_test/fly_catkin_ws/src/vitfly
logdir = training/logs
datadir = training/datasets

# 数据集配置
dataset = data
short = 0
val_split = 0.2
seed = 42

# 模型配置
model_type = ViT
load_checkpoint = False
checkpoint_path = ''

# 训练模式配置
# 可选: 'stage1' (仅模仿学习), 'stage2' (仅强化学习), 'both' (完整流程)
training_mode = both

# 第一阶段：模仿学习参数
stage1_epochs = 100
stage1_lr = 1e-4
stage1_lr_warmup_epochs = 5
stage1_lr_decay = False
stage1_save_freq = 25
stage1_val_freq = 10

# 第二阶段：强化学习参数
stage2_episodes = 1000
stage2_lr_actor = 1e-5
stage2_lr_critic = 1e-4
stage2_gamma = 0.99
stage2_tau = 0.005
stage2_batch_size = 256
stage2_buffer_size = 100000
stage2_eval_freq = 100

# 环境配置
env_config = flightmare/flightpy/configs/vision/config.yaml

# 工作区后缀
ws_suffix = _two_stage
