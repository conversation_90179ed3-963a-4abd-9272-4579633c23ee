"""
强化学习环境包装器
将Flightmare的VisionEnv封装成符合强化学习标准的环境

@authors: Based on Flightmare VisionEnv
@organization: GRASP Lab, University of Pennsylvania
@brief: 环境包装器，提供标准的RL接口
"""

import numpy as np
import torch
import cv2
import sys
import os
from os.path import join as opj

# 添加flightmare路径
sys.path.append(opj(os.path.dirname(os.path.abspath(__file__)), '../flightmare/flightlib/build'))

try:
    import flightgym
except ImportError:
    print("Warning: flightgym not found. Please build flightmare first.")
    flightgym = None

class FlightmareEnvWrapper:
    """
    Flightmare环境包装器
    提供标准的强化学习环境接口
    """
    
    def __init__(self, config_path=None, env_id=0, img_width=90, img_height=60):
        """
        初始化环境
        
        Args:
            config_path: 配置文件路径
            env_id: 环境ID
            img_width: 图像宽度
            img_height: 图像高度
        """
        self.env_id = env_id
        self.img_width = img_width
        self.img_height = img_height
        
        # 默认配置路径
        if config_path is None:
            config_path = opj(os.path.dirname(os.path.abspath(__file__)), 
                            '../flightmare/flightpy/configs/vision/config.yaml')
        
        self.config_path = config_path
        
        # 初始化环境
        if flightgym is not None:
            self.env = flightgym.VisionEnv_v1(config_path, env_id)
        else:
            self.env = None
            print("Warning: Using dummy environment")
        
        # 环境参数
        self.max_episode_steps = 1000
        self.current_step = 0
        
        # 状态空间和动作空间定义
        self.observation_space_dim = 15 + 10 * 4  # 基础观测 + 10个障碍物状态
        self.action_space_dim = 3  # [vx, vy, vz]
        self.action_bound = 1.0
        
        # 奖励函数参数（基于config.yaml）
        self.reward_params = {
            'vel_coeff': -0.01,
            'collision_coeff': -10.0,  # 增强碰撞惩罚
            'angular_vel_coeff': -0.0001,
            'survive_rew': 0.03,
            'progress_coeff': 0.01,
            'smoothness_coeff': -0.001
        }
        
        # 目标速度
        self.goal_velocity = np.array([1.0, 0.0, 0.0])
        
        # 状态历史（用于平滑性奖励）
        self.action_history = []
        self.max_history_length = 5
        
    def reset(self):
        """
        重置环境
        
        Returns:
            observation: 初始观测
        """
        self.current_step = 0
        self.action_history = []
        
        if self.env is not None:
            # 重置Flightmare环境
            obs = np.zeros(self.observation_space_dim)
            self.env.reset(obs)
            
            # 获取深度图像
            depth_img = np.zeros((self.img_height, self.img_width), dtype=np.float32)
            self.env.getDepthImage(depth_img)
            
            # 获取状态信息
            quad_state = np.zeros(25)  # QuadState::SIZE
            self.env.getQuadState(quad_state)
            
            # 构建完整观测
            observation = self._build_observation(depth_img, obs, quad_state)
        else:
            # 虚拟环境
            observation = self._get_dummy_observation()
        
        return observation
    
    def step(self, action):
        """
        执行一步动作
        
        Args:
            action: 动作向量 [vx, vy, vz]
            
        Returns:
            observation: 下一步观测
            reward: 奖励
            done: 是否结束
            info: 额外信息
        """
        self.current_step += 1
        
        # 限制动作范围
        action = np.clip(action, -self.action_bound, self.action_bound)
        
        # 记录动作历史
        self.action_history.append(action.copy())
        if len(self.action_history) > self.max_history_length:
            self.action_history.pop(0)
        
        if self.env is not None:
            # 执行动作
            obs = np.zeros(self.observation_space_dim)
            reward_vec = np.zeros(5)  # [vel_reward, collision_penalty, ang_vel_penalty, survive_rew, total]
            
            # 将速度命令转换为四元数控制命令
            # 这里简化处理，实际应该根据当前姿态计算
            control_cmd = np.array([action[0], action[1], action[2], 0.0])  # [vx, vy, vz, yaw_rate]
            
            self.env.step(control_cmd, obs, reward_vec)
            
            # 获取深度图像
            depth_img = np.zeros((self.img_height, self.img_width), dtype=np.float32)
            self.env.getDepthImage(depth_img)
            
            # 获取状态信息
            quad_state = np.zeros(25)
            self.env.getQuadState(quad_state)
            
            # 检查碰撞
            is_collision = self.env.isCollision()
            
            # 构建观测
            observation = self._build_observation(depth_img, obs, quad_state)
            
            # 计算奖励
            reward = self._compute_reward(quad_state, action, is_collision)
            
            # 检查终止条件
            done = self._is_done(quad_state, is_collision)
            
            info = {
                'collision': is_collision,
                'position': quad_state[0:3],
                'velocity': quad_state[7:10],
                'episode_step': self.current_step
            }
        else:
            # 虚拟环境
            observation = self._get_dummy_observation()
            reward = np.random.random() - 0.5
            done = self.current_step >= self.max_episode_steps
            info = {'collision': False}
        
        return observation, reward, done, info
    
    def _build_observation(self, depth_img, env_obs, quad_state):
        """
        构建完整的观测
        
        Args:
            depth_img: 深度图像
            env_obs: 环境观测
            quad_state: 四旋翼状态
            
        Returns:
            observation: 格式化的观测，适用于ViT模型
        """
        # 预处理深度图像
        depth_img = cv2.resize(depth_img, (self.img_width, self.img_height))
        depth_img = depth_img.astype(np.float32) / 255.0  # 归一化
        depth_img = depth_img[np.newaxis, :]  # 添加通道维度
        
        # 提取期望速度（标量）
        desired_speed = np.linalg.norm(self.goal_velocity)
        
        # 提取四元数
        quaternion = quad_state[3:7]  # [qw, qx, qy, qz]
        
        # 返回ViT模型期望的格式
        observation = [
            torch.from_numpy(depth_img).float(),  # 深度图像
            torch.from_numpy(np.array([desired_speed])).float(),  # 期望速度
            torch.from_numpy(quaternion).float()  # 四元数
        ]
        
        return observation
    
    def _compute_reward(self, quad_state, action, is_collision):
        """
        计算奖励函数
        
        Args:
            quad_state: 四旋翼状态
            action: 执行的动作
            is_collision: 是否碰撞
            
        Returns:
            reward: 总奖励
        """
        # 提取状态信息
        position = quad_state[0:3]
        velocity = quad_state[7:10]
        angular_velocity = quad_state[10:13]
        
        # 1. 速度跟踪奖励
        vel_error = np.linalg.norm(velocity - self.goal_velocity)
        vel_reward = self.reward_params['vel_coeff'] * vel_error
        
        # 2. 碰撞惩罚
        collision_penalty = self.reward_params['collision_coeff'] * float(is_collision)
        
        # 3. 角速度惩罚（平滑性）
        ang_vel_penalty = self.reward_params['angular_vel_coeff'] * np.linalg.norm(angular_velocity)
        
        # 4. 生存奖励
        survive_reward = self.reward_params['survive_rew']
        
        # 5. 前进奖励
        progress_reward = self.reward_params['progress_coeff'] * velocity[0]  # x方向速度
        
        # 6. 动作平滑性奖励
        smoothness_penalty = 0.0
        if len(self.action_history) > 1:
            action_diff = np.linalg.norm(self.action_history[-1] - self.action_history[-2])
            smoothness_penalty = self.reward_params['smoothness_coeff'] * action_diff
        
        # 总奖励
        total_reward = (vel_reward + collision_penalty + ang_vel_penalty + 
                       survive_reward + progress_reward + smoothness_penalty)
        
        return total_reward
    
    def _is_done(self, quad_state, is_collision):
        """
        检查是否终止
        
        Args:
            quad_state: 四旋翼状态
            is_collision: 是否碰撞
            
        Returns:
            done: 是否终止
        """
        # 碰撞终止
        if is_collision:
            return True
        
        # 超出边界终止
        position = quad_state[0:3]
        if (position[0] < -20 or position[0] > 80 or
            position[1] < -10 or position[1] > 10 or
            position[2] < 0 or position[2] > 15):
            return True
        
        # 超过最大步数
        if self.current_step >= self.max_episode_steps:
            return True
        
        return False
    
    def _get_dummy_observation(self):
        """获取虚拟观测（用于测试）"""
        depth_img = np.random.random((1, self.img_height, self.img_width)).astype(np.float32)
        desired_speed = 1.0
        quaternion = np.array([1.0, 0.0, 0.0, 0.0])
        
        return [
            torch.from_numpy(depth_img).float(),
            torch.from_numpy(np.array([desired_speed])).float(),
            torch.from_numpy(quaternion).float()
        ]
    
    def close(self):
        """关闭环境"""
        if self.env is not None:
            # Flightmare环境的清理
            pass
    
    def render(self, mode='human'):
        """渲染环境（可选）"""
        pass
