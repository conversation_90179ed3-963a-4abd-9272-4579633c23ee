"""
两阶段训练主脚本
启动模仿学习 + 强化学习的完整训练流程

@authors: Based on original training framework
@organization: GRASP Lab, University of Pennsylvania
@brief: 主训练脚本，支持两阶段训练配置
"""

import os
import sys
import torch
import configargparse
import getpass
from os.path import join as opj

# 导入两阶段训练器
from two_stage_trainer import TwoStageTrainer

# 获取当前用户名
uname = getpass.getuser()

def argparsing():
    """参数解析"""
    parser = configargparse.ArgumentParser()
    
    # 配置文件
    parser.add_argument('--config', is_config_file=True, 
                       help='config file relative path')
    
    # 基础路径配置
    parser.add_argument('--basedir', type=str, 
                       default=f'/home/<USER>/agile_ws/src/agile_flight',
                       help='path to repo')
    parser.add_argument('--logdir', type=str, 
                       default='training/logs',
                       help='path to relative logging directory')
    parser.add_argument('--datadir', type=str, 
                       default='training/datasets',
                       help='path to relative dataset directory')
    
    # 实验配置
    parser.add_argument('--ws_suffix', type=str, default='_two_stage',
                       help='suffix for workspace name')
    parser.add_argument('--model_type', type=str, default='ViT',
                       help='model type: ViT, ViTLSTM, etc.')
    parser.add_argument('--dataset', type=str, default='data',
                       help='name of dataset')
    parser.add_argument('--short', type=int, default=0,
                       help='if nonzero, how many trajectory folders to load')
    parser.add_argument('--val_split', type=float, default=0.2,
                       help='fraction of dataset to use for validation')
    parser.add_argument('--seed', type=int, default=42,
                       help='random seed')
    parser.add_argument('--device', type=str, default='cuda',
                       help='device: cuda or cpu')
    
    # 第一阶段：模仿学习参数
    parser.add_argument('--stage1_epochs', type=int, default=100,
                       help='number of epochs for imitation learning')
    parser.add_argument('--stage1_lr', type=float, default=1e-4,
                       help='learning rate for imitation learning')
    parser.add_argument('--stage1_lr_warmup_epochs', type=int, default=5,
                       help='warmup epochs for imitation learning')
    parser.add_argument('--stage1_lr_decay', action='store_true', default=False,
                       help='use lr decay in imitation learning')
    parser.add_argument('--stage1_save_freq', type=int, default=25,
                       help='save frequency for imitation learning')
    parser.add_argument('--stage1_val_freq', type=int, default=10,
                       help='validation frequency for imitation learning')
    
    # 第二阶段：强化学习参数
    parser.add_argument('--stage2_episodes', type=int, default=1000,
                       help='number of episodes for reinforcement learning')
    parser.add_argument('--stage2_lr_actor', type=float, default=1e-5,
                       help='actor learning rate for RL')
    parser.add_argument('--stage2_lr_critic', type=float, default=1e-4,
                       help='critic learning rate for RL')
    parser.add_argument('--stage2_gamma', type=float, default=0.99,
                       help='discount factor for RL')
    parser.add_argument('--stage2_tau', type=float, default=0.005,
                       help='soft update coefficient for RL')
    parser.add_argument('--stage2_batch_size', type=int, default=256,
                       help='batch size for RL training')
    parser.add_argument('--stage2_buffer_size', type=int, default=100000,
                       help='replay buffer size for RL')
    parser.add_argument('--stage2_eval_freq', type=int, default=100,
                       help='evaluation frequency for RL')
    
    # 训练模式选择
    parser.add_argument('--training_mode', type=str, default='both',
                       choices=['stage1', 'stage2', 'both'],
                       help='training mode: stage1 (imitation only), stage2 (RL only), both (full pipeline)')
    parser.add_argument('--stage1_checkpoint', type=str, default='',
                       help='path to stage1 checkpoint (for stage2 only mode)')
    
    # 环境配置
    parser.add_argument('--env_config', type=str, 
                       default='flightmare/flightpy/configs/vision/config.yaml',
                       help='path to environment config file')
    
    # 其他配置
    parser.add_argument('--load_checkpoint', action='store_true', default=False,
                       help='whether to load from checkpoint')
    parser.add_argument('--checkpoint_path', type=str, default='',
                       help='path to checkpoint')
    
    args = parser.parse_args()
    
    # 设置一些兼容性参数（为了兼容原始TRAINER类）
    args.lr = args.stage1_lr
    args.N_eps = args.stage1_epochs
    args.lr_warmup_epochs = args.stage1_lr_warmup_epochs
    args.lr_decay = args.stage1_lr_decay
    args.save_model_freq = args.stage1_save_freq
    args.val_freq = args.stage1_val_freq
    
    return args

def main():
    """主函数"""
    # 设置默认张量类型
    if torch.cuda.is_available():
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
    
    # 解析参数
    args = argparsing()
    print(f"[MAIN] 训练参数: {args}")
    
    # 设置随机种子
    if args.seed is not None:
        torch.manual_seed(args.seed)
        import numpy as np
        import random
        np.random.seed(args.seed)
        random.seed(args.seed)
    
    # 创建两阶段训练器
    trainer = TwoStageTrainer(args)
    
    try:
        if args.training_mode == 'stage1':
            # 仅运行第一阶段
            print("[MAIN] 运行模式: 仅模仿学习")
            stage1_model = trainer.train_stage1_imitation_learning()
            print(f"[MAIN] 模仿学习完成，模型保存在: {stage1_model}")
            
        elif args.training_mode == 'stage2':
            # 仅运行第二阶段
            print("[MAIN] 运行模式: 仅强化学习")
            if not args.stage1_checkpoint:
                raise ValueError("stage2模式需要提供stage1_checkpoint参数")
            
            trainer.stage1_model_path = args.stage1_checkpoint
            trainer.stage1_completed = True
            stage2_model = trainer.train_stage2_reinforcement_learning(args.stage2_episodes)
            print(f"[MAIN] 强化学习完成，模型保存在: {stage2_model}")
            
        elif args.training_mode == 'both':
            # 运行完整的两阶段训练
            print("[MAIN] 运行模式: 完整两阶段训练")
            stage1_model, stage2_model = trainer.run_full_training(args.stage2_episodes)
            print(f"[MAIN] 两阶段训练完成!")
            print(f"[MAIN] 模仿学习模型: {stage1_model}")
            print(f"[MAIN] 强化学习模型: {stage2_model}")
        
        else:
            raise ValueError(f"不支持的训练模式: {args.training_mode}")
    
    except Exception as e:
        print(f"[MAIN] 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("[MAIN] 训练完成!")
    return 0

if __name__ == '__main__':
    exit_code = main()
