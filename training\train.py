"""
@authors: <PERSON>
@organization: GRASP Lab, University of Pennsylvania
@date: ...
@license: ...

@brief: 此模块包含论文 "Utilizing vision transformer models for end-to-end vision-based
quadrotor obstacle avoidance" 中使用的训练程序。
"""

import os, sys
from os.path import join as opj
import numpy as np
import torch
from datetime import datetime
import time
from torch.utils.tensorboard import SummaryWriter
import torch.nn as nn
import torch.nn.functional as F

from dataloading import *
# 将模型所在目录添加到系统路径，以便后续导入模型
sys.path.append(opj(os.path.dirname(os.path.abspath(__file__)), '../models'))
import model as model_library

# 抑制 TensorFlow 的警告和信息日志
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
import getpass
# 获取当前用户名
uname = getpass.getuser()

# 一个用于训练网络从深度图像预测动作的类
# 训练器可以通过两种方式加载：
# 1. 仅用于数据加载，此时提供 dataset_name 且通常 no_model=True
# 2. 用于模型训练，此时仅提供 args
class TRAINER:
    def __init__(self, args=None):
        self.args = args
        if self.args is not None:
            # 从参数中获取设备信息，如 'cuda' 或 'cpu'
            self.device = args.device
            # 项目基础目录
            self.basedir = args.basedir
            # 日志目录
            self.logdir = args.logdir
            # 数据集目录
            self.datadir = args.datadir
            # 工作区名称后缀
            self.ws_suffix = args.ws_suffix
            # 数据集名称
            self.dataset_name = args.dataset
            # 若不为 0，表示加载的轨迹文件夹数量
            self.short = args.short

            # 模型类型
            self.model_type = args.model_type
            # 验证集划分比例
            self.val_split = args.val_split
            # 随机种子
            self.seed = args.seed # if args.seed>0 else None
            # 是否从检查点加载模型
            self.load_checkpoint = args.load_checkpoint
            # 检查点文件路径
            self.checkpoint_path = args.checkpoint_path
            # 学习率
            self.lr = args.lr
            # 训练的总轮数
            self.N_eps = args.N_eps
            # 学习率热身的轮数
            self.lr_warmup_epochs = args.lr_warmup_epochs
            # 是否使用学习率衰减
            self.lr_decay = args.lr_decay
            # 保存模型的频率
            self.save_model_freq = args.save_model_freq
            # 验证模型的频率
            self.val_freq = args.val_freq

        else:
            # 若未提供参数，抛出异常
            raise Exception("Args are not provided")

        # 确保提供了数据集名称
        assert self.dataset_name is not None, 'Dataset name not provided, neither through args nor through dataset_name kwarg'

        ###############
        ## Workspace ##
        ###############

        # 生成实验名称，包含日期和时间
        expname = datetime.now().strftime('d%m_%d_t%H_%M')
        # 构建工作区路径
        self.workspace = opj(self.basedir, self.logdir, expname)
        wkspc_ctr = 2
        # 若工作区已存在，添加序号后缀
        while os.path.exists(self.workspace):
            self.workspace = opj(self.basedir, self.logdir, expname+f'_{str(wkspc_ctr)}')
            wkspc_ctr += 1
        # 添加工作区后缀
        self.workspace = self.workspace + self.ws_suffix
        # 创建工作区目录
        os.makedirs(self.workspace)
        # 初始化 TensorBoard 日志写入器
        self.writer = SummaryWriter(self.workspace)

        # 保存有序参数、配置文件和日志文件
        if self.args is not None:
            f = opj(self.workspace, 'args.txt')
            with open(f, 'w') as file:
                # 将参数写入文件
                for arg in sorted(vars(self.args)):
                    attr = getattr(self.args, arg)
                    file.write('{} = {}\n'.format(arg, attr))
                f = opj(self.workspace, 'config.txt')
            with open(f, 'w') as file:
                # 将配置文件内容写入文件
                file.write(open(self.args.config, 'r').read())
        f = opj(self.workspace, 'log.txt')
        # 打开日志文件，用于记录训练信息
        self.logfile = open(f, 'w')

        # 记录创建工作区的日志
        self.mylogger(f'[LearnerLSTM init] Making workspace {self.workspace}')

        # 构建数据集目录路径
        self.dataset_dir = opj(self.datadir, self.dataset_name)

        #################
        ## Dataloading ##
        #################

        if self.load_checkpoint:
            print('[LearnerLSTM init] Loading train_val_dirs from checkpoint')
            try:
                # 从检查点所在目录加载训练集和验证集的目录信息
                train_val_dirs = tuple(np.load(opj(os.path.dirname(self.checkpoint_path), 'train_val_dirs.npy'), allow_pickle=True))
            except:
                print('[LearnerLSTM init] Could not load train_val_dirs from checkpoint, dataloading from scratch')
                train_val_dirs = None
        else:
            train_val_dirs = None

        # 加载数据
        self.dataloader(val_split=self.val_split, short=self.short, seed=self.seed, train_val_dirs=train_val_dirs)

        # 训练步骤数，硬编码为轨迹数量
        self.num_training_steps = self.train_trajlength.shape[0]
        # 验证步骤数
        self.num_val_steps = self.val_trajlength.shape[0]
        # 学习率热身的迭代次数
        self.lr_warmup_iters = self.lr_warmup_epochs * self.num_training_steps

        ##################################
        ## Define network and optimizer ##
        ##################################

        # 记录建立模型和优化器的日志
        self.mylogger('[SETUP] Establishing model and optimizer.')
        if self.model_type == 'LSTMNet':
            # 初始化 LSTMNet 模型并移动到指定设备
            self.model = model_library.LSTMNet().to(self.device).float()
        elif self.model_type == 'ConvNet':
            # 初始化 ConvNet 模型并移动到指定设备
            self.model = model_library.ConvNet().to(self.device).float()
        elif self.model_type == 'ViT':
            # 初始化 ViT 模型并移动到指定设备
            self.model = model_library.ViT().to(self.device).float()
        elif self.model_type == 'ViTLSTM':
            # 初始化 LSTMNetVIT 模型并移动到指定设备
            self.model = model_library.LSTMNetVIT().to(self.device).float()
        elif self.model_type == 'UNet':
            # 初始化 UNetConvLSTMNet 模型并移动到指定设备
            self.model = model_library.UNetConvLSTMNet().to(self.device).float()
        elif self.model_type == 'WindowsViT':
            # 初始化 WindowsViT 模型并移动到指定设备
            self.model = model_library.WindowsViT().to(self.device).float()
        elif self.model_type == 'LSTMWindowsViT':
            # 初始化 LSTMWindowsViT 模型并移动到指定设备
            self.model = model_library.LSTMWindowsViT().to(self.device).float()
        elif self.model_type == 'LSTMWindowsGoT':
            # 初始化 LSTMWindowsGoT 模型并移动到指定设备
            self.model = model_library.LSTMWindowsGoT().to(self.device).float()
        else:
            # 若模型类型无效，记录日志并退出
            self.mylogger(f'[SETUP] Invalid model_type {self.model_type}. Exiting.')
            exit()

        # 初始化 Adam 优化器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr)

        # 已训练的轮数
        self.num_eps_trained = 0
        if self.load_checkpoint:
            # 从检查点加载模型
            self.load_from_checkpoint(self.checkpoint_path)

        # 总迭代次数
        self.total_its = self.num_eps_trained * self.num_training_steps

    def mylogger(self, msg):
        # 打印日志信息并写入日志文件
        print(msg)
        self.logfile.write(msg+'\n')

    def load_from_checkpoint(self, checkpoint_path):
        try:
            # 从检查点路径解析已训练的轮数
            self.num_eps_trained = int(checkpoint_path[-10:-4])
        except:
            self.num_eps_trained = 0
            # 若解析失败，记录日志并使用 0
            self.mylogger(f'[SETUP] Could not parse number of epochs trained from checkpoint path {checkpoint_path}, using 0')
        # 记录从检查点加载模型的日志
        self.mylogger(f'[SETUP] Loading checkpoint from {checkpoint_path}, already trained for {self.num_eps_trained} epochs')
        # 加载模型状态字典
        self.model.load_state_dict(torch.load(checkpoint_path, map_location=self.device))

    def dataloader(self, val_split, short=0, seed=None, train_val_dirs=None):
        # 记录数据加载的日志
        self.mylogger(f'[DATALOADER] Loading from {self.dataset_dir}')
        # 加载训练集、验证集数据，判断图像格式，获取训练集和验证集目录
        train_data, val_data, is_png, (self.train_dirs, self.val_dirs) = dataloader(opj(self.basedir, self.dataset_dir), val_split=val_split, short=short, seed=seed, train_val_dirs=train_val_dirs)
        self.train_meta, self.train_ims, self.train_trajlength, self.train_desvel, self.train_currquat, self.train_currctbr = train_data
        self.val_meta, self.val_ims, self.val_trajlength, self.val_desvel, self.val_currquat, self.val_currctbr = val_data
        # 记录数据加载完成的日志
        self.mylogger(f'[DATALOADER] Dataloading done | train images {self.train_ims.shape}, val images {self.val_ims.shape}')

        # 将数据预加载到指定设备
        self.train_meta, self.train_ims, self.train_desvel, self.train_currquat, self.train_currctbr = preload((self.train_meta, self.train_ims, self.train_desvel, self.train_currquat, self.train_currctbr), self.device)
        self.val_meta, self.val_ims, self.val_desvel, self.val_currquat, self.val_currctbr = preload((self.val_meta, self.val_ims, self.val_desvel, self.val_currquat, self.val_currctbr), self.device)
        # 记录数据预加载完成的日志
        self.mylogger(f'[DATALOADER] Preloading into device {self.device} done')

        # 确保训练图像数据已归一化
        assert self.train_ims.max() <= 1.0 and self.train_ims.min() >= 0.0, 'Images not normalized (values outside [0.0, 1.0])'
        assert self.train_ims.max() > 0.50, "Images not normalized (values only below 0.10, possibly due to not normalizing images from 'old' dataset)"

        # 提取训练集和验证集的速度控制命令
        self.train_velcmd = self.train_meta[:, range(13, 16) if is_png else range(12, 15)]
        self.val_velcmd = self.val_meta[:, range(13, 16) if is_png else range(12, 15)]

        # 将训练集和验证集目录保存到工作区
        np.save(opj(self.workspace, 'train_val_dirs.npy'), np.array((self.train_dirs, self.val_dirs), dtype=object))

    def lr_scheduler(self, it):
        if it < self.lr_warmup_iters:
            # 学习率热身阶段，学习率线性增加
            lr = (0.9*self.lr)/self.lr_warmup_iters * it + 0.1*self.lr
        else:
            if self.lr_decay:
                # 学习率衰减阶段，指数衰减
                lr = self.lr * (0.1 ** ((it-self.lr_warmup_iters) / (self.N_eps*self.num_training_steps)))
            else:
                # 不使用学习率衰减，保持初始学习率
                lr = self.lr
        return lr

    def save_model(self, ep):
        # 记录保存模型的日志
        self.mylogger(f'[SAVE] Saving model at epoch {ep}')
        path = self.workspace
        # 保存模型状态字典
        torch.save(self.model.state_dict(), opj(path, f'model_{str(ep).zfill(6)}.pth'))
        # 记录模型保存完成的日志
        self.mylogger(f'[SAVE] Model saved at {path}')

    def weighted_mse_loss(self, input, target, weight):
        # 计算加权均方误差损失
        return torch.mean(weight * (input - target) ** 2)

    def train(self):
        
        self.mylogger(f'[TRAIN] Training for {self.N_eps} epochs')
        train_start = time.time()

       
        self.train_traj_starts = np.cumsum(self.train_trajlength) - self.train_trajlength
        train_traj_lengths = self.train_trajlength

        for ep in range(self.num_eps_trained, self.num_eps_trained + self.N_eps):

            # 定期保存模型检查点
            if ep % self.save_model_freq == 0 and ep - self.num_eps_trained > 0:
                self.save_model(ep)

            # 定期在验证集上评估模型
            if ep % self.val_freq == 0:
                self.validation(ep)

            ep_loss = 0
            gradnorm = 0


            shuffled_traj_indices = np.random.permutation(len(self.train_traj_starts))
            train_traj_starts = self.train_traj_starts[shuffled_traj_indices]
            train_traj_lengths = self.train_trajlength[shuffled_traj_indices]

            ### Training loop ###
            # 设置模型为训练模式
            self.model.train()
            for it in range(self.num_training_steps):
                # 清零优化器梯度
                self.optimizer.zero_grad()

                traj_input = self.train_ims[train_traj_starts[it]+1 : train_traj_starts[it]+train_traj_lengths[it], :, :].unsqueeze(1) #1, traj,

                desvel = self.train_desvel[train_traj_starts[it]+1 : train_traj_starts[it]+train_traj_lengths[it]].view(-1, 1)

                currquat = self.train_currquat[train_traj_starts[it]+1 : train_traj_starts[it]+train_traj_lengths[it]]

                pred, _ = self.model([traj_input, desvel, currquat]) #, (init_hidden_state, init_cell_state)])
      
                cmd = self.train_velcmd[train_traj_starts[it]+1 : train_traj_starts[it]+train_traj_lengths[it], :]
          
                cmd_norm = cmd / desvel # normalize each row by each desvel element
                cmd_norm = cmd_norm
                # 计算损失
                loss = F.mse_loss(cmd_norm, pred)
                ep_loss += loss
                # 反向传播
                loss.backward()
                # 梯度裁剪并计算梯度范数
                gradnorm += torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=torch.inf)
                # 更新模型参数
                self.optimizer.step()
                # 计算新的学习率
                new_lr = self.lr_scheduler(self.total_its-self.num_eps_trained*self.num_training_steps)
                # 更新优化器的学习率
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = new_lr

                self.total_its += 1

            # 计算平均损失和平均梯度范数
            ep_loss /= self.num_training_steps
            gradnorm /= self.num_training_steps

            # 记录训练完成一个 epoch 的日志
            self.mylogger(f'[TRAIN] Completed epoch {ep + 1}/{self.num_eps_trained + self.N_eps}, ep_loss = {ep_loss:.6f}, time = {time.time() - train_start:.2f}s, time/epoch = {(time.time() - train_start)/(ep + 1 - self.num_eps_trained):.2f}s')

            # 记录训练损失、梯度范数和学习率到 TensorBoard
            self.writer.add_scalar('train/loss', ep_loss, ep)
            self.writer.add_scalar('train/gradnorm', gradnorm, ep)
            self.writer.add_scalar('train/lr', new_lr, self.total_its)
            self.writer.flush()

        # 记录训练完成的日志
        self.mylogger(f'[TRAIN] Training complete, total time = {time.time() - train_start:.2f}s')
        # 保存最终模型
        self.save_model(ep)

    def validation(self, ep):
        # 记录开始验证的日志
        self.mylogger(f'[VAL] Validating for val set of size {self.val_ims.shape[0]} images')

        val_start = time.time()
        it = 1

        with torch.no_grad():

            ep_loss = 0

            # 验证集中各轨迹的起始索引
            val_traj_starts = np.cumsum(self.val_trajlength) - self.val_trajlength
            # 在末尾添加 -1 作为结束标记
            val_traj_starts = np.hstack((val_traj_starts, -1))

            ### Validation loop ###
            # 设置模型为评估模式
            self.model.eval()

            for it in range(self.num_val_steps):
                # 提取当前轨迹的输入图像
                traj_input = self.val_ims[val_traj_starts[it]+1 : val_traj_starts[it]+self.val_trajlength[it], :, :].unsqueeze(1)
                # 提取当前轨迹的期望速度
                desvel = self.val_desvel[val_traj_starts[it]+1 : val_traj_starts[it]+self.val_trajlength[it]].view(-1, 1)
                # 提取当前轨迹的当前四元数
                currquat = self.val_currquat[val_traj_starts[it]+1 : val_traj_starts[it]+self.val_trajlength[it]]
                # 模型前向传播
                pred, _ = self.model([traj_input, desvel, currquat]) #, (init_hidden_state, init_cell_state)])
                # 提取当前轨迹的速度控制命令
                cmd = self.val_velcmd[val_traj_starts[it]+1 : val_traj_starts[it]+self.val_trajlength[it], :]
                # 对速度控制命令进行归一化
                cmd_norm = cmd / desvel # normalize each row by each desvel element
                # 计算损失
                loss = F.mse_loss(cmd_norm, pred)
                ep_loss += loss

            # 计算平均验证损失
            ep_loss /= (it+1)

            # 记录验证完成的日志
            self.mylogger(f'[VAL] Completed validation, val_loss = {ep_loss:.6f}, time taken = {time.time() - val_start:.2f} s')
            # 记录验证损失到 TensorBoard
            self.writer.add_scalar('val/loss', ep_loss, ep)

def argparsing():

    import configargparse
    # 初始化参数解析器
    parser = configargparse.ArgumentParser()

    # 通用参数
    # 配置文件的相对路径
    parser.add_argument('--config', is_config_file=True, help='config file relative path')
    # 项目仓库路径
    parser.add_argument('--basedir', type=str, default=f'/home/<USER>/agile_ws/src/agile_flight', help='path to repo')
    # 相对日志目录路径
    parser.add_argument('--logdir', type=str, default='learner/logs', help='path to relative logging directory')
    # 相对数据集目录路径
    parser.add_argument('--datadir', type=str, default=f'/home/<USER>/agile_ws/src/agile_flight', help='path to relative dataset directory')

    # 实验级和学习器参数
    # 工作区名称后缀
    parser.add_argument('--ws_suffix', type=str, default='', help='suffix if any to workspace name')
    # 模型类型，需与 lstmArch.py 中的模型名称匹配
    parser.add_argument('--model_type', type=str, default='LSTMNet', help='string matching model name in lstmArch.py')
    # 数据集名称
    parser.add_argument('--dataset', type=str, default='5-2', help='name of dataset')
    # 若不为 0，表示加载的轨迹文件夹数量
    parser.add_argument('--short', type=int, default=0, help='if nonzero, how many trajectory folders to load')
    # 验证集在数据集中所占比例
    parser.add_argument('--val_split', type=float, default=0.2, help='fraction of dataset to use for validation')
    # 随机种子
    parser.add_argument('--seed', type=int, default=None, help='random seed to use for python random, numpy, and torch -- WARNING, probably not fully implemented')
    # 通用 CUDA 设备，具体 GPU 需在 CUDA_VISIBLE_DEVICES 中指定
    parser.add_argument('--device', type=str, default='cuda', help='generic cuda device; specific GPU should be specified in CUDA_VISIBLE_DEVICES')
    # 是否从模型检查点加载
    parser.add_argument('--load_checkpoint', action='store_true', default=False, help='whether to load from a model checkpoint')
    # 模型检查点的绝对路径
    parser.add_argument('--checkpoint_path', type=str, default=f'/home/<USER>/agile_ws/src/agile_flight/learner/logs/d05_10_t03_13/model_000499.pth', help='absolute path to model checkpoint')
    # 学习率
    parser.add_argument('--lr', type=float, default=1e-4, help='learning rate')
    # 训练的总轮数
    parser.add_argument('--N_eps', type=int, default=100, help='number of epochs to train for')
    # 学习率热身的轮数
    parser.add_argument('--lr_warmup_epochs', type=int, default=5, help='number of epochs to warmup learning rate for')
    # 是否使用学习率衰减
    parser.add_argument('--lr_decay', action='store_true', default=False, help='whether to use lr_decay, hardcoded to exponentially decay to 0.01 * lr by end of training')
    # 保存模型检查点的频率
    parser.add_argument('--save_model_freq', type=int, default=25, help='frequency with which to save model checkpoints')
    # 验证模型的频率
    parser.add_argument('--val_freq', type=int, default=10, help='frequency with which to evaluate on validation set')

    # 解析命令行参数
    args = parser.parse_args()
    print(f'[CONFIGARGPARSE] Parsing args from config file {args.config}')

    return args

if __name__ == '__main__':
    # 设置默认张量类型为 CUDA FloatTensor
    torch.set_default_tensor_type('torch.cuda.FloatTensor')

    # 解析命令行参数
    args = argparsing()
    print(args)

    # 初始化训练器
    learner = TRAINER(args)
    # 开始训练
    learner.train()
