"""
@authors: <PERSON>, et. al
@organization: GRASP Lab, University of Pennsylvania
@date: ...
@license: ...

@brief: This module contains the submodules for ViT that were used in the paper "Utilizing vision transformer models for end-to-end vision-based
quadrotor obstacle avoidance" by <PERSON><PERSON><PERSON><PERSON>, et. al

@source: https://github.com/git-dhruv/Segformer

@updated: 整合了vheat_vit.py中的Heat2D机制，支持热传导方程替代自注意力
@features:
    - 支持非正方形深度图像输入 [B, 15*23, 32]
    - 提供EfficientSelfAttention和Heat2D两种注意力机制
    - 完整的中文注释和输入输出形状说明
"""

import math
import torch
import numpy as np
import torch.nn as nn
from einops import rearrange
import torch.nn.functional as F
from functools import partial
from typing import Optional, Callable
try:
    from timm.models.layers import DropPath, trunc_normal_
except ImportError:
    # 如果timm不可用，提供简单的替代实现
    class DropPath(nn.Module):
        def __init__(self, drop_prob=0.):
            super().__init__()
            self.drop_prob = drop_prob

        def forward(self, x):
            if self.drop_prob == 0. or not self.training:
                return x
            keep_prob = 1 - self.drop_prob
            shape = (x.shape[0],) + (1,) * (x.ndim - 1)
            random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
            random_tensor.floor_()
            output = x.div(keep_prob) * random_tensor
            return output

    def trunc_normal_(tensor, mean=0., std=1., a=-2., b=2.):
        """截断正态分布初始化的简单实现"""
        with torch.no_grad():
            tensor.normal_(mean, std)
            tensor.clamp_(min=a, max=b)
        return tensor

def get_relative_distances(window_height, window_width):
    """
    生成窗口内像素的相对位置坐标差矩阵
    参数：
        window_size: 窗口大小（正方形窗口边长）
    返回：
        distances: 相对位置坐标差矩阵 [window_size^2, window_size^2, 2]
                  每个元素[i,j]表示第i个位置与第j个位置的(x,y)坐标差
    """
    # 生成网格坐标：创建window_size x window_size网格中每个像素的坐标
    indices = torch.tensor(np.array(
        [[x, y] for x in range(window_height) for y in range(window_width)]))
    # 计算所有位置对的坐标差：通过广播机制实现矩阵减法
    distances = indices[None, :, :] - indices[:, None, :]
    return distances

class RadixSoftmax(nn.Module):
    """
    RadixSoftmax模块：实现多头注意力中的径向softmax操作
    参数：
        radix: 径向数量
        cardinality: 卡尔基数
    """
    def __init__(self, radix, cardinality):
        super(RadixSoftmax, self).__init__()
        self.radix = radix
        self.cardinality = cardinality

    def forward(self, x):
        """
        前向传播函数
        参数：
            x: 输入张量
        返回：
            输出张量
        """
        x = torch.sigmoid(x)  # 使用sigmoid激活函数
        return x

class SplitAttn(nn.Module):
    """
    Split Attention模块：实现多头注意力中的分裂注意力机制
    参数：
        input_dim: 输入维度
    """
    def __init__(self, input_dim):
        super(SplitAttn, self).__init__()
        self.input_dim = input_dim

        self.fc1 = nn.Linear(input_dim, input_dim, bias=False)
        self.bn1 = nn.LayerNorm(input_dim)
        self.act1 = nn.ReLU()
        self.fc2 = nn.Linear(input_dim, input_dim * 2, bias=False)  # 从3改为2

        self.rsoftmax = RadixSoftmax(2, 1)  # 从3改为2

    def forward(self, window_list):
        """
        前向传播函数
        参数：
            window_list: 包含多个窗口特征的列表，每个窗口特征形状为(B, H, W, C)
        返回：
            输出张量
        """
        # window list: [(B, H, W, C) * 2]
        assert len(window_list) == 2, 'only 2 windows are supported'  # 从3改为2

        sw, mw = window_list[0], window_list[1]  # 只使用两个窗口
        B, H = sw.shape[0], sw.shape[1]

        # global average pooling, B, L, H, W, C
        x_gap = sw + mw  # 只有两个窗口相加
        # B, 1, 1, C
        x_gap = x_gap.mean((1, 2), keepdim=True)
        x_gap = self.act1(self.bn1(self.fc1(x_gap)))
        # B, 1, 1, 2C
        x_attn = self.fc2(x_gap)
        # B 1 1 2C
        x_attn = self.rsoftmax(x_attn).view(B, 1, 1, -1)

        out = sw * x_attn[:, :, :, 0:self.input_dim] + \
              mw * x_attn[:, :, :, self.input_dim:2*self.input_dim]

        return out

class OverlapPatchMerging(nn.Module):
    """
    重叠补丁合并层
    功能：
        将输入图像分割成重叠的补丁，并通过卷积操作降低维度
    参数：
        in_channels: 输入通道数
        out_channels: 输出通道数(嵌入维度)
        patch_size: 补丁大小(卷积核大小)
        stride: 步长，控制补丁之间的重叠程度
        padding: 填充大小
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding):
        super().__init__()
        # 卷积层：用于提取补丁并降低维度
        self.cn1 = nn.Conv2d(in_channels, out_channels, kernel_size=patch_size, stride=stride, padding=padding)
        # 层归一化：对每个位置的特征进行归一化
        self.layerNorm = nn.LayerNorm(out_channels)

    def forward(self, patches):
        """
        前向传播函数
        参数：
            patches: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度
        返回：
            形状为(B, N, EmbedDim)的张量，其中
                N是补丁数量(H * W)
                EmbedDim是嵌入维度(out_channels)
        """
        x = self.cn1(patches)  # 输入形状(B, C, H, W)，输出形状(B, out_channels, H', W')
        _, _, H, W = x.shape
        # 将特征图展平为序列形式，形状变为(B, H' * W', out_channels)
        x = x.flatten(2).transpose(1, 2)
        # 应用层归一化
        x = self.layerNorm(x)
        return x, H, W  # 返回(B, N, EmbedDim)和特征图尺寸

class OverlapPatchMerging2(nn.Module):
    """
    重叠补丁合并层

    这个层将输入图像分割成重叠的补丁(patch)，并通过卷积操作降低维度
    与标准ViT不同，这里使用重叠的补丁而不是不重叠的补丁，可以保留更多的空间信息
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding):
        """
        初始化重叠补丁合并层

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数(嵌入维度)
            patch_size: 补丁大小(卷积核大小)
            stride: 步长，控制补丁之间的重叠程度
            padding: 填充大小
        """
        super().__init__()
        # 使用卷积层来实现补丁提取和特征映射
        self.cn1 = nn.Conv2d(in_channels, out_channels, kernel_size=patch_size, stride=stride, padding=padding)
        # 层归一化，应用于嵌入维度
        self.layerNorm = nn.LayerNorm(out_channels)

    def forward(self, patches):
        """
        前向传播函数

        参数:
            patches: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度

        返回:
            x: 形状为(B, N, E)的张量，其中
                N是补丁数量(H*W)
                E是嵌入维度(out_channels)
            H: 输出特征图的高度
            W: 输出特征图的宽度
        """
        # 通过卷积提取补丁特征
        x = self.cn1(patches)
        # 获取输出特征图的形状
        _, _, H, W = x.shape
        # 将特征图重塑为序列形式
        # 从(B,C,H,W)变为(B,H*W,C)，即(批次,序列长度,嵌入维度)
        x = rearrange(x, 'b c h w -> b h w c')
        # x = x.transpose(1, 2)  # 先展平为(B,C,H*W)，再转置为(B,H*W,C)
        # 应用层归一化
        x = self.layerNorm(x)
        # 返回序列表示和特征图尺寸
        return x  # 返回(B,N,E)和特征图尺寸

class SEModule(nn.Module):
    def __init__(self, in_channels, reduction_ratio=2):
        super(SEModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class EfficientSelfAttention(nn.Module):
    def __init__(self, channels, reduction_ratio, num_heads):
        super().__init__()
        assert channels % num_heads == 0, f"channels {channels} should be divided by num_heads {num_heads}."

        self.heads= num_heads

        #### Self Attention Block consists of 2 parts - Reduction and then normal Attention equation of queries and keys###

        # Reduction Parameters #
        self.cn1 = nn.Conv2d(in_channels=channels, out_channels=channels, kernel_size=reduction_ratio, stride= reduction_ratio)
        self.ln1 = nn.LayerNorm(channels)
        # Attention Parameters #
        self.keyValueExtractor = nn.Linear(channels, channels * 2)
        self.query = nn.Linear(channels, channels)
        self.smax = nn.Softmax(dim=-1)
        self.finalLayer = nn.Linear(channels, channels)


    def forward(self, x, H, W):

        """ Perform self attention with reduced sequence length

        :param x: tensor of shape (B, N, C) where
            B is the batch size,
            N is the number of queries (equal to H * W)
            C is the number of channels
        :return: tensor of shape (B, N, C)
        """
        B,N,C = x.shape
        # B, N, C -> B, C, N
        x1 = x.clone().permute(0,2,1)
        # BCN -> BCHW
        x1 = x1.reshape(B,C,H,W)
        x1 = self.cn1(x1)
        x1 = x1.reshape(B,C,-1).permute(0,2,1).contiguous()
        x1 = self.ln1(x1)
        # We have got the Reduced Embeddings! We need to extract key and value pairs now
        keyVal = self.keyValueExtractor(x1)
        keyVal = keyVal.reshape(B, -1 , 2, self.heads, int(C/self.heads)).permute(2,0,3,1,4).contiguous()
        k,v = keyVal[0],keyVal[1] #b,heads, n, c/heads
        q = self.query(x).reshape(B, N, self.heads, int(C/self.heads)).permute(0, 2, 1, 3).contiguous()

        dimHead = (C/self.heads)**0.5
        attention = self.smax(<EMAIL>(-2, -1)/dimHead)
        attention = (attention@v).transpose(1,2).reshape(B,N,C)

        x = self.finalLayer(attention) #B,N,C
        return x

class SpatialWindowAttention(nn.Module):
    """
    空间窗口注意力模块，用于在特定窗口内计算自注意力机制。
    该模块通过降维操作减少计算量，同时引入相对位置编码增强注意力计算。
    """
    def __init__(self, channels, reduction_ratio, num_heads, window_size):
        """
        初始化空间窗口注意力模块。

        参数:
            channels: 输入特征的通道数。
            reduction_ratio: 降维比例，用于空间降采样。
            num_heads: 注意力头的数量。
            window_size: 窗口大小，用于划分特征图。
        """
        super().__init__()

        assert channels % num_heads == 0, f"channels {channels} should be divided by num_heads {num_heads}."

        self.heads = num_heads
        self.window_size = window_size

        #### 自注意力块由两部分组成 - 降维和标准的注意力计算 ###

        # 降维参数 #
        # 使用卷积进行空间降采样，减少序列长度
        self.cn1 = nn.Conv2d(in_channels=channels, out_channels=channels,
                            kernel_size=reduction_ratio, padding=(reduction_ratio-1)//2)
        self.ln1 = nn.LayerNorm(channels)

        self.relative_indices = get_relative_distances(window_size, window_size) + \
                                    window_size - 1
        # 可学习的位置偏置参数矩阵（形状根据相对位置范围确定）
        self.pos_embedding = nn.Parameter(torch.randn(2 * window_size - 1,
                                                          2 * window_size - 1))

        # 注意力参数 #
        # 从降采样的特征中提取键和值
        self.keyValueExtractor = nn.Linear(channels, channels * 2)  # 输出channels*2是因为同时生成key和value
        # 从原始特征中提取查询
        self.query = nn.Linear(channels, channels)

        # softmax用于注意力权重归一化
        self.smax = nn.Softmax(dim=-1)
        # 最终投影层
        self.finalLayer = nn.Linear(channels, channels)

    def forward(self, x):
        """
        前向传播函数。

        参数:
            x: 输入特征张量，形状为 (B, H, W, C)，其中
                B 是批次大小，
                H 是特征图高度，
                W 是特征图宽度，
                C 是通道数。

        返回:
            输出特征张量，形状为 (B, H, W, C)。
        """
        B, H, W, C = x.shape
        m = self.heads
        new_h = H // self.window_size
        new_w = W // self.window_size

        # 假设输入 x 形状为 (B, 60, 90, C)
        # 创建x的副本并重排维度，准备进行空间降采样
        # 从(B,H,W,C)变为(B,C,H,W)
        x1 = x.clone().permute(0, 3, 1, 2)  # 形状: (B, C, 60, 90)

        # 应用卷积进行降采样
        x1 = self.cn1(x1)  # 形状: (B, C, H', W')，H' 和 W' 由卷积参数决定
        # 假设 reduction_ratio 为 2，padding 为 0，H' = (60 - 2) // 2 + 1 = 30，W' = (90 - 2) // 2 + 1 = 45
        # 即形状变为 (B, C, 30, 45)

        # 重塑回序列形式(B,C,H',W') -> (B,H',W',C)
        x1 = x1.permute(0, 2, 3, 1).contiguous()  # 形状: (B, 30, 45, C)

        # 应用层归一化
        x1 = self.ln1(x1)  # 形状: (B, 30, 45, C)

        # 从降采样的特征中提取键和值
        kv = self.keyValueExtractor(x1).chunk(2, dim=-1)  # chunk返回元组，包含 key 和 value，每个形状为 (B, 30, 45, C)

        # 复杂的重塑操作，将key和value分离，并为多头注意力做准备
        # 最终形状: (B, m, new_h * new_w, window_size * window_size, C/m)
        k, v = map(
            lambda t: rearrange(t, 'b (new_h w_h) (new_w w_w) (m c) -> b m (new_h new_w) (w_h w_w) c',
                                m=m, w_h=self.window_size,
                                w_w=self.window_size,
                                new_h=new_h, new_w=new_w), kv)
        # 假设 window_size 为 10，new_h = 60 // 10 = 6，new_w = 90 // 10 = 9
        # k 和 v 形状: (B, m, 6 * 9, 10 * 10, C/m) = (B, m, 54, 100, C/m)

        # 从原始特征中提取查询
        q = self.query(x).contiguous()
        q = rearrange(q, 'b (new_h w_h) (new_w w_w) (m c) -> b m (new_h new_w) (w_h w_w) c',
                      m=m, w_h=self.window_size, w_w=self.window_size)
        # q 形状: (B, m, 54, 100, C/m)

        # 计算注意力权重的缩放因子
        dimHead = (C / self.heads) ** 0.5

        dots = q @ k.transpose(-2, -1)  # 形状: (B, m, 54, 100, 100)
        dots += self.pos_embedding[self.relative_indices[:, :, 0],
                                   self.relative_indices[:, :, 1]]

        attention = self.smax(dots / dimHead)  # 形状: (B, m, 54, 100, 100)

        attention = attention @ v  # 形状: (B, m, 54, 100, C/m)
        attention = rearrange(attention, 'b m (new_h new_w) (w_h w_w) c -> b (new_h w_h) (new_w w_w) (m c)', m=m,
                              w_h=self.window_size, w_w=self.window_size, new_h=new_h, new_w=new_w)
        # 形状: (B, 60, 90, C)

        # 通过最终投影层
        x = self.finalLayer(attention)  # 形状: (B, 60, 90, C)
        return x




class MixFFN(nn.Module):
    def __init__(self, channels, expansion_factor):
        super().__init__()
        expanded_channels = channels*expansion_factor
        #MLP Layer
        self.mlp1 = nn.Linear(channels, expanded_channels)
        #Depth Wise CNN Layer
        self.depthwise = nn.Conv2d(expanded_channels, expanded_channels, kernel_size=3,  padding='same', groups=channels)
        #GELU
        self.gelu = nn.GELU()
        #MLP to predict
        self.mlp2 = nn.Linear(expanded_channels, channels)

    def forward(self, x, H, W):
        """ Perform self attention with reduced sequence length

        :param x: tensor with shape (B, C, H, W) where
            B is the Batch size
            C is the number of Channels
            H and W are the Height and Width
        :return: tensor with shape (B, C, H, W)
        """
        # Input BNC instead of BCHW
        # BNC -> B,N,C*exp
        x = self.mlp1(x)
        B,N,C = x.shape
        # Prepare for the CNN operation, channel should be 1st dim
        # B,N, C*exp -> B, C*exp, H, W
        x = x.transpose(1,2).view(B,C,H,W)

        #Depth Conv - B, N, Cexp
        x = self.gelu(self.depthwise(x).flatten(2).transpose(1,2))

        #Back to the orignal shape
        x = self.mlp2(x) # BNC
        return x

class MixFFN2(nn.Module):
    """
    混合前馈网络

    这是一种结合了MLP和深度卷积的前馈网络，可以同时捕获通道间和空间信息
    相比标准Transformer中的纯MLP前馈网络，这种混合结构更适合处理图像数据
    """
    def __init__(self, channels, expansion_factor):
        """
        初始化混合前馈网络

        参数:
            channels: 输入通道数(嵌入维度)
            expansion_factor: 扩展因子，控制中间层的通道数扩展比例
        """
        super().__init__()
        # 计算扩展后的通道数
        expanded_channels = channels * expansion_factor

        # MLP层 - 将通道数扩展
        self.mlp1 = nn.Linear(channels, expanded_channels)

        # 深度卷积层 - 在扩展的通道上进行空间处理
        # groups=channels确保这是深度可分离卷积，每个通道独立卷积
        self.depthwise = nn.Conv2d(expanded_channels, expanded_channels,
                                  kernel_size=3, padding='same', groups=expanded_channels)

        # GELU激活函数 - 比ReLU更平滑的非线性激活
        self.gelu = nn.GELU()

        # 第二个MLP层 - 将通道数恢复到原始维度
        self.mlp2 = nn.Linear(expanded_channels, channels)

    def forward(self, x):
        """
        前向传播函数

        参数:
            x: 形状为(B, N, C)的张量，其中
                B是批次大小
                N是序列长度(补丁数量)
                C是通道数(嵌入维度)
            H: 特征图高度
            W: 特征图宽度

        返回:
            形状为(B, N, C)的张量，与输入形状相同
        """
        # 输入是(B,N,C)形式，而不是(B,C,H,W)

        # 通过第一个MLP层扩展通道维度
        # (B,N,C) -> (B,N,C*expansion_factor)
        x = self.mlp1(x)
        B, H, W, C = x.shape

        # 为卷积操作准备数据，需要将通道维度放在第二位
        # (B,N,C*exp) -> (B,C*exp,N) -> (B,C*exp,H,W)
        # x = x.transpose(1, 2).view(B, C, H, W)
        x = x.permute(0, 3, 1, 2)
        # 应用深度卷积，然后展平并转置回序列形式
        # (B,C*exp,H,W) -> (B,C*exp,H,W) -> (B,C*exp,N) -> (B,N,C*exp)
        # x = self.gelu(self.depthwise(x).flatten(2).transpose(1, 2))
        x = self.gelu(self.depthwise(x)).permute(0, 2, 3, 1)
        # 通过第二个MLP层恢复原始通道数
        # (B,N,C*exp) -> (B,N,C)
        x = self.mlp2(x)
        return x


class MixTransformerEncoderLayer(nn.Module):
    def __init__(self, in_channels, out_channels, patch_size, stride, padding,
                 n_layers, reduction_ratio, num_heads, expansion_factor):
        super().__init__()
        # 补丁合并层：将输入图像转换为序列形式
        self.patchMerge = OverlapPatchMerging(in_channels, out_channels, patch_size, stride, padding)
        # 自注意力层列表：每个层包含一个自注意力模块
        # self._attn = nn.ModuleList([EfficientSelfAttention(out_channels, reduction_ratio, num_heads) for _ in range(n_layers)])
        self._attn = nn.ModuleList([EfficientSelfAttention(out_channels, reduction_ratio, num_heads) for _ in range(n_layers)])
        # 前馈网络层列表：每个层包含一个前馈网络模块
        self._ffn = nn.ModuleList([MixFFN(out_channels, expansion_factor) for _ in range(n_layers)])
        # 层归一化层列表：每个层包含一个层归一化模块
        self._lNorm = nn.ModuleList([nn.LayerNorm(out_channels) for _ in range(n_layers)])

    def forward(self, x):
        """
        运行一个混合视觉Transformer块。

        :param x: 形状为(B, C, H, W)的张量，其中
            B是批次大小
            C是通道数
            H和W是高度和宽度
        :return: 形状为(B, C, H, W)的张量
        """
        B, C, H, W = x.shape  # 输入形状为(B, C, H, W)
        x, H, W = self.patchMerge(x)  # 补丁合并后，形状变为(B, N, EmbedDim)
        for i in range(len(self._attn)):
            # 自注意力层后，形状保持不变(B, N, EmbedDim)
            x = x + self._attn[i].forward(x, H, W)
            # 前馈网络层后，形状保持不变(B, N, EmbedDim)
            x = x + self._ffn[i].forward(x, H, W)
            # 层归一化后，形状保持不变(B, N, EmbedDim)
            x = self._lNorm[i].forward(x)
        # 将序列形式转换回空间形式，形状变为(B, C, H, W)
        x = x.reshape(B, H, W, -1).permute(0, 3, 1, 2).contiguous()
        return x


class HeatMixTransformerEncoderLayer(nn.Module):
    """
    基于热传导方程的混合Transformer编码器层

    这是 MixTransformerEncoderLayer 的变体，仅将注意力机制从 EfficientSelfAttention
    替换为 Heat2D_ViT，其他所有组件（OverlapPatchMerging、MixFFN、LayerNorm）保持不变。

    输入格式: [B, C, H, W] (标准图像格式)
    输出格式: [B, out_channels, H', W'] (降采样后的特征图)

    特别适配深度图像处理，支持非正方形输入如 [B, 32, 15, 23]

    架构对比:
    - 原始: OverlapPatchMerging + EfficientSelfAttention + MixFFN + LayerNorm
    - 新版: OverlapPatchMerging + Heat2D_ViT + MixFFN + LayerNorm

    主要优势:
    1. 保持与原始 MixTransformerEncoderLayer 完全相同的接口
    2. 利用热传导方程的物理先验知识进行特征变换
    3. 支持非正方形输入，特别适合深度图像 [B, 15*23, 32]
    4. 在频域进行特征处理，具有更好的全局感受野
    """

    def __init__(self,
                 in_channels,
                 out_channels,
                 patch_size,
                 stride,
                 padding,
                 n_layers,
                 reduction_ratio,  # 注意：此参数在Heat2D中不使用，但保留以维持接口兼容性
                 num_heads,       # 注意：此参数在Heat2D中不使用，但保留以维持接口兼容性
                 expansion_factor,
                 resolution=(15, 23),  # 新增：空间分辨率，默认适配深度图像
                 infer_mode=False,     # 新增：推理模式，用于Heat2D优化
                 **kwargs):
        """
        初始化基于热传导方程的混合Transformer编码器层

        Args:
            in_channels: 输入通道数，对于深度图像通常为32
            out_channels: 输出通道数(嵌入维度)，通常为64/128/256/512
            patch_size: 补丁大小，用于OverlapPatchMerging
            stride: 步长，用于OverlapPatchMerging
            padding: 填充大小，用于OverlapPatchMerging
            n_layers: 内部Transformer块的数量
            reduction_ratio: 保留参数以维持接口兼容性（Heat2D中不使用）
            num_heads: 保留参数以维持接口兼容性（Heat2D中不使用）
            expansion_factor: 前馈网络中的扩展因子，用于MixFFN
            resolution: 空间分辨率(H, W)，对于深度图像默认为(15, 23)
            infer_mode: 推理模式，True时预计算权重以加速推理
        """
        super().__init__()

        # 保存配置参数
        self.n_layers = n_layers
        self.out_channels = out_channels

        # 支持矩形分辨率，特别适配深度图像的非正方形输入
        if isinstance(resolution, int):
            self.resolution = (resolution, resolution)
        else:
            self.resolution = tuple(resolution)

        # 补丁合并层：与原始版本完全相同，将输入图像转换为序列形式
        # 输入: [B, in_channels, H, W] -> 输出: [B, H', W', out_channels]
        self.patchMerge = OverlapPatchMerging(in_channels, out_channels, patch_size, stride, padding)

        # Heat2D注意力层列表：替换原来的EfficientSelfAttention
        # 注意：这里不设置固定的resolution，而是在forward中动态传递实际的特征图尺寸
        # 因为OverlapPatchMerging会改变空间尺寸，我们需要使用降采样后的实际尺寸
        self._attn = nn.ModuleList([
            Heat2D_ViT(
                dim=out_channels,           # 特征维度
                resolution=(1, 1),          # 临时设置，实际使用时会动态传递正确尺寸
                infer_mode=infer_mode      # 推理模式
            )
            for _ in range(n_layers)
        ])

        # 前馈网络层列表：与原始版本完全相同
        # 使用MixFFN进行通道间的特征变换
        self._ffn = nn.ModuleList([
            MixFFN(out_channels, expansion_factor)
            for _ in range(n_layers)
        ])

        # 层归一化层列表：与原始版本完全相同
        # 用于稳定训练和提高性能
        self._lNorm = nn.ModuleList([
            nn.LayerNorm(out_channels)
            for _ in range(n_layers)
        ])

        # 保存配置用于调试和可视化
        self.config = {
            'attention_type': 'heat2d',
            'resolution': self.resolution,
            'n_layers': n_layers,
            'out_channels': out_channels,
            'in_channels': in_channels
        }

    def forward(self, x, freq_embeds=None):
        """
        基于热传导方程的混合Transformer编码器层前向传播

        与原始 MixTransformerEncoderLayer.forward() 保持完全相同的接口和行为，
        仅将内部的注意力机制从 EfficientSelfAttention 替换为 Heat2D_ViT。

        Args:
            x: 输入张量，形状为(B, C, H, W)，其中
                B是批次大小
                C是通道数，对于深度图像通常为32
                H和W是高度和宽度，对于深度图像通常为15和23
            freq_embeds: 可选的频率嵌入列表，用于Heat2D的自适应衰减控制
                        每个元素形状为[H', W', out_channels]，长度为n_layers
                        如果为None，则使用默认的衰减权重

        Returns:
            输出张量，形状为(B, out_channels, H', W')，其中
                H'和W'是经过OverlapPatchMerging降采样后的尺寸

        处理流程（修正版本）:
        1. 通过OverlapPatchMerging进行补丁合并和降维，得到实际的特征图尺寸(H', W')
        2. 对每个层依次执行：
           - Heat2D注意力变换（使用实际的H', W'尺寸）+ 残差连接
           - MixFFN前馈网络变换 + 残差连接
           - LayerNorm层归一化
        3. 将序列格式转换回空间格式输出

        关键修正:
        - Heat2D_ViT 使用的是降采样后的实际尺寸 (H', W')，而不是原始输入尺寸 (H, W)
        - 确保序列长度 H'*W' 与 Heat2D 内部的空间形状计算一致

        形状变换详解:
        输入: [B, C, H, W] = [B, 32, 15, 23] (深度图像示例)
        ↓ OverlapPatchMerging (stride=2)
        序列: [B, H'*W', out_channels] = [B, 8*12, 64] (H'≈15/2=8, W'≈23/2=12)
        ↓ n_layers × (Heat2D + MixFFN + LayerNorm)
        序列: [B, H'*W', out_channels] = [B, 8*12, 64] (形状保持不变)
        ↓ reshape + permute
        输出: [B, out_channels, H', W'] = [B, 64, 8, 12]
        """
        # 步骤1: 通过补丁合并层，将图像转换为序列表示
        # 输入: [B, C, H, W] -> 输出: [B, H'*W', out_channels] + H', W'
        # 这里的 H_new, W_new 是经过降采样后的实际特征图尺寸
        x, H_new, W_new = self.patchMerge(x)  # 补丁合并后，形状变为(B, H'*W', out_channels)
        B = x.shape[0]  # 获取批次大小

        # 步骤2: 依次通过每个Transformer层
        for i in range(len(self._attn)):
            # Heat2D注意力层 + 残差连接
            # 关键修正：使用降采样后的实际尺寸 (H_new, W_new)
            freq_embed = freq_embeds[i] if freq_embeds is not None else None
            x = x + self._attn[i].forward(x, H_new, W_new, freq_embed, (H_new, W_new))

            # 前馈网络层 + 残差连接
            # 注意：MixFFN需要H, W参数进行空间维度的处理
            x = x + self._ffn[i].forward(x, H_new, W_new)

            # 层归一化
            # 注意：LayerNorm直接作用于最后一个维度，不需要额外参数
            x = self._lNorm[i].forward(x)

        # 步骤3: 将序列形式转换回空间形式
        # [B, H'*W', out_channels] -> [B, H', W', out_channels] -> [B, out_channels, H', W']
        x = x.reshape(B, H_new, W_new, -1).permute(0, 3, 1, 2).contiguous()

        return x

class PyramidWindowAttention(nn.Module):
    def __init__(self, channels, reduction_ratio, num_heads, window_size,
                  fuse_method='split_attn'):
        super().__init__()

        self.pwmsa = nn.ModuleList([])
        self.window_sizes = window_size

        for (num_head, ws) in zip(num_heads, window_size):
            self.pwmsa.append(SpatialWindowAttention(channels,
                                                     reduction_ratio,
                                                     num_head,
                                                     ws))
        self.fuse_method = fuse_method
        if fuse_method == 'split_attn':
            self.split_attn = SplitAttn(channels)

        # 存储融合权重
        self.fusion_weights = None

    def forward(self, x):
        output = None
        # naive fusion will just sum up all window attention output and do a
        # mean
        if self.fuse_method == 'naive':
            for wmsa in self.pwmsa:
                output = wmsa(x) if output is None else output + wmsa(x)
            return output / len(self.pwmsa)

        elif self.fuse_method == 'split_attn':
            window_list = [wmsa(x) for wmsa in self.pwmsa]
            return self.split_attn(window_list)





class MixWindowsTransformerEncoderLayer(nn.Module):
    """
    混合Transformer编码器层

    这是整个Vision Transformer的主要构建块，组合了补丁合并、自注意力和前馈网络
    可以堆叠多个这样的层来构建完整的Vision Transformer网络
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding,
                 n_layers, reduction_ratio, num_heads, expansion_factor, window_size):
        """
        初始化混合Transformer编码器层

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数(嵌入维度)
            patch_size: 补丁大小
            stride: 步长
            padding: 填充大小
            n_layers: 内部Transformer块的数量
            reduction_ratio: 自注意力中的降采样比例
            num_heads: 注意力头的数量列表，每个窗口大小对应一个头数
            expansion_factor: 前馈网络中的扩展因子
            window_size: 窗口大小列表，用于多尺度窗口注意力
        """
        super().__init__()
        # 补丁合并层 - 将输入图像转换为序列表示
        self.patchMerge = OverlapPatchMerging2(in_channels, out_channels, patch_size, stride, padding)

        # 创建n_layers个自注意力层、前馈网络和层归一化
        # 注: 这里使用ModuleList而不是更简洁的实现，是因为每个前向函数的输入不同

        # 自注意力层列表
        self._attn = nn.ModuleList([
            PyramidWindowAttention(out_channels, reduction_ratio, num_heads, window_size)
            for _ in range(n_layers)
        ])

        # 前馈网络列表
        self._ffn = nn.ModuleList([
            MixFFN2(out_channels, expansion_factor)
            for _ in range(n_layers)
        ])

        # 层归一化列表
        self._lNorm = nn.ModuleList([
            nn.LayerNorm(out_channels)
            for _ in range(n_layers)
        ])

        # 保存窗口大小和层数，用于可视化
        self.window_size = window_size
        self.n_layers = n_layers

    def forward(self, x):
        """
        前向传播函数

        参数:
            x: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度

        返回:
            形状为(B, out_channels, H', W')的张量，其中H'和W'是降采样后的尺寸
        """
        # 通过补丁合并层，将图像转换为序列表示
        # (B,C,H,W) -> (B,H,W,C)，其中H,W是降采样后的高度和宽度
        x = self.patchMerge(x)

        # 依次通过每个Transformer块
        for i in range(len(self._attn)):
            # 残差连接 + 自注意力
            x = x + self._attn[i].forward(x)

            # 残差连接 + 前馈网络
            x = x + self._ffn[i].forward(x)

            # 层归一化
            x = self._lNorm[i].forward(x)

        # 将序列表示转换回空间表示
        x = x.permute(0, 3, 1, 2).contiguous()

        return x


# ================================
# Heat2D 热传导方程注意力机制模块
# ================================

class Heat2D_ViT(nn.Module):
    """
    Heat2D模块，适配ViT架构，用热传导方程替代自注意力机制

    输入格式: [B, H*W, C] (序列格式，B=批次大小，H*W=patch数量，C=特征维度)
    输出格式: [B, H*W, C] (保持相同的序列格式)

    基于热传导方程的解析解:
    偏微分方程: du/dt - k(d²u/dx² + d²u/dy²) = 0
    解析解: u(x,y,t) = Σ A_nm * cos(nπx/a) * cos(mπy/b) * exp(-[(nπ/a)² + (mπ/b)²]kt)

    核心思想:
    1. 使用DCT将空间域信号转换到频域
    2. 在频域应用热传导衰减（低频保留，高频衰减）
    3. 使用IDCT转换回空间域

    支持矩形输入，不再限制为正方形，特别适配深度图像 [B, 15*23, 32]
    """

    def __init__(self, dim=768, hidden_dim=None, resolution=(14, 14), infer_mode=False, **kwargs):
        """
        初始化Heat2D模块

        Args:
            dim (int): 输入特征维度，默认768
            hidden_dim (int): 隐藏层维度，默认等于dim
            resolution (tuple or int): 空间分辨率，支持(H, W)或单个int（正方形）
                                     对于深度图像，通常为(15, 23)
            infer_mode (bool): 推理模式，预计算衰减权重以加速推理
        """
        super().__init__()
        hidden_dim = hidden_dim or dim
        self.dim = dim
        self.hidden_dim = hidden_dim

        # 支持矩形分辨率，特别适配深度图像的非正方形输入
        if isinstance(resolution, int):
            self.resolution = (resolution, resolution)
        else:
            self.resolution = tuple(resolution)

        self.infer_mode = infer_mode

        # 线性投影层
        self.norm = nn.LayerNorm(dim)  # 输入归一化: [B, H*W, C] -> [B, H*W, C]
        self.linear = nn.Linear(dim, 2 * hidden_dim, bias=True)  # 特征扩展: [B, H*W, C] -> [B, H*W, 2*hidden_dim]
        self.out_norm = nn.LayerNorm(hidden_dim)  # 输出归一化: [B, H*W, hidden_dim] -> [B, H*W, hidden_dim]
        self.out_linear = nn.Linear(hidden_dim, dim, bias=True)  # 特征压缩: [B, H*W, hidden_dim] -> [B, H*W, C]

        # 频率嵌入变换网络，用于生成自适应的衰减系数
        self.to_k = nn.Sequential(
            nn.Linear(dim, hidden_dim, bias=True),  # [H, W, C] -> [H, W, hidden_dim]
            nn.ReLU(),
        )

    def infer_init_heat2d(self, freq_embed):
        """
        推理模式初始化，预计算衰减权重以加速推理

        Args:
            freq_embed: [H, W, C] 频率嵌入张量
        """
        weight_exp = self.get_decay_map(self.resolution, device=freq_embed.device)
        # 预计算自适应衰减权重: [H, W, hidden_dim]
        self.k_exp = nn.Parameter(
            torch.pow(weight_exp[:, :, None], self.to_k(freq_embed)),
            requires_grad=False
        )
        del self.to_k  # 删除不再需要的网络以节省内存

    @staticmethod
    def get_cos_map(N=14, device=torch.device("cpu"), dtype=torch.float):
        """
        生成DCT/IDCT变换矩阵

        Args:
            N (int): 变换大小
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [N, N] DCT变换矩阵

        数学原理: DCT基函数 cos((x + 0.5) / N * n * π)
        """
        # 生成DCT变换矩阵，用于空间域到频域的转换
        weight_x = (torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(1, -1) + 0.5) / N
        weight_n = torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(-1, 1)
        weight = torch.cos(weight_n * weight_x * torch.pi) * math.sqrt(2 / N)
        weight[0, :] = weight[0, :] / math.sqrt(2)  # DC分量归一化
        return weight

    @staticmethod
    def get_decay_map(resolution=(14, 14), device=torch.device("cpu"), dtype=torch.float):
        """
        生成基于热传导方程的指数衰减图

        Args:
            resolution (tuple): (H, W) 空间分辨率，支持非正方形如(15, 23)
            device: 计算设备
            dtype: 数据类型

        Returns:
            torch.Tensor: [H, W] 衰减权重图

        数学原理: exp(-[(nπ/a)² + (mπ/b)²])
        低频分量(n,m接近0)衰减小，高频分量衰减大
        """
        resh, resw = resolution
        # 生成频率坐标
        weight_n = torch.linspace(0, torch.pi, resh + 1, device=device, dtype=dtype)[:resh].view(-1, 1)
        weight_m = torch.linspace(0, torch.pi, resw + 1, device=device, dtype=dtype)[:resw].view(1, -1)
        # 计算频率的平方和并应用指数衰减
        weight = torch.pow(weight_n, 2) + torch.pow(weight_m, 2)
        weight = torch.exp(-weight)
        return weight

    def forward(self, x: torch.Tensor, H=None, W=None, freq_embed=None, spatial_shape=None):
        """
        Heat2D前向传播，实现基于热传导方程的特征变换

        Args:
            x: [B, H*W, C] 输入序列格式张量 (B=批次大小, H*W=patch数量, C=特征维度)
            H: 空间高度（与EfficientSelfAttention接口兼容）
            W: 空间宽度（与EfficientSelfAttention接口兼容）
            freq_embed: [H, W, C] 可选的频率嵌入，用于自适应衰减控制
            spatial_shape: (H, W) 空间形状，如果为None则尝试从H,W或resolution推断

        Returns:
            [B, H*W, C] 输出序列格式张量，维度与输入相同

        处理流程:
        1. 序列格式 -> 空间格式
        2. 特征变换和门控准备
        3. DCT变换到频域
        4. 应用热传导衰减
        5. IDCT变换回空间域
        6. 门控和输出投影
        7. 空间格式 -> 序列格式
        """
        B, HW, C = x.shape

        # 确定空间形状，优先级：spatial_shape > H,W参数 > self.resolution
        if spatial_shape is not None:
            H_actual, W_actual = spatial_shape
        elif H is not None and W is not None:
            H_actual, W_actual = H, W
        else:
            H_actual, W_actual = self.resolution

        assert H_actual * W_actual == HW, f"序列长度 {HW} 与空间形状 {H_actual}x{W_actual} 不匹配"

        # 步骤1: 重塑为空间格式以便进行热传导处理
        x = x.view(B, H_actual, W_actual, C)  # [B, H, W, C]

        # 步骤2: 应用归一化和线性变换
        x = self.norm(x)  # 层归一化
        x = self.linear(x)  # [B, H, W, 2*hidden_dim] 特征扩展
        x, z = x.chunk(chunks=2, dim=-1)  # 分离为主分支x和门控分支z，各为[B, H, W, hidden_dim]

        # 步骤3: 获取或缓存DCT变换矩阵（性能优化）
        if ((H_actual, W_actual) == getattr(self, "__RES__", (0, 0))) and (getattr(self, "__WEIGHT_COSN__", None) is not None):
            # 使用缓存的变换矩阵
            weight_cosn = getattr(self, "__WEIGHT_COSN__", None)
            weight_cosm = getattr(self, "__WEIGHT_COSM__", None)
            weight_exp = getattr(self, "__WEIGHT_EXP__", None)
        else:
            # 重新计算并缓存变换矩阵
            weight_cosn = self.get_cos_map(H_actual, device=x.device).detach_()  # [H, H] DCT矩阵
            weight_cosm = self.get_cos_map(W_actual, device=x.device).detach_()  # [W, W] DCT矩阵
            weight_exp = self.get_decay_map((H_actual, W_actual), device=x.device).detach_()  # [H, W] 衰减图
            setattr(self, "__RES__", (H_actual, W_actual))
            setattr(self, "__WEIGHT_COSN__", weight_cosn)
            setattr(self, "__WEIGHT_COSM__", weight_cosm)
            setattr(self, "__WEIGHT_EXP__", weight_exp)

        N, M = weight_cosn.shape[0], weight_cosm.shape[0]

        # 步骤4: 前向DCT变换 - 空间域到频域
        # 沿H维度进行DCT: [B, H, W*hidden_dim] -> [B, N, W*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, H_actual, -1), weight_cosn.contiguous().view(N, H_actual, 1))
        # 沿W维度进行DCT: [B*N, W, hidden_dim] -> [B*N, M, hidden_dim] -> [B, N, M, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, W_actual, self.hidden_dim), weight_cosm.contiguous().view(M, W_actual, 1)).contiguous().view(B, N, M, -1)

        # 步骤5: 在频域应用热传导衰减
        if self.infer_mode:
            # 推理模式：使用预计算的自适应衰减权重
            x = torch.einsum("bnmc,nmc->bnmc", x, self.k_exp)
        else:
            # 训练模式：动态计算自适应衰减权重
            if freq_embed is not None:
                # 使用频率嵌入生成自适应衰减权重: [H, W] -> [H, W, hidden_dim]
                weight_exp = torch.pow(weight_exp[:, :, None], self.to_k(freq_embed))
            else:
                # 如果没有频率嵌入，扩展weight_exp的维度以匹配hidden_dim
                weight_exp = weight_exp[:, :, None].expand(-1, -1, self.hidden_dim)
            # 应用衰减: [B, N, M, hidden_dim] * [N, M, hidden_dim] -> [B, N, M, hidden_dim]
            x = torch.einsum("bnmc,nmc->bnmc", x, weight_exp)

        # 步骤6: 逆DCT变换 - 频域到空间域
        # 沿N维度进行IDCT: [B, N, M*hidden_dim] -> [B, H, M*hidden_dim]
        x = F.conv1d(x.contiguous().view(B, N, -1), weight_cosn.t().contiguous().view(H_actual, N, 1))
        # 沿M维度进行IDCT: [B*H, M, hidden_dim] -> [B*H, W, hidden_dim] -> [B, H, W, hidden_dim]
        x = F.conv1d(x.contiguous().view(-1, M, self.hidden_dim), weight_cosm.t().contiguous().view(W_actual, M, 1)).contiguous().view(B, H_actual, W_actual, -1)

        # 步骤7: 应用输出归一化和门控机制
        x = self.out_norm(x)  # 层归一化
        x = x * F.silu(z)  # SiLU门控：主分支与门控分支相乘
        x = self.out_linear(x)  # 输出投影: [B, H, W, hidden_dim] -> [B, H, W, C]

        # 步骤8: 重塑回序列格式
        x = x.view(B, HW, C)  # [B, H*W, C]

        return x









# ================================
# 使用示例和测试代码
# ================================

if __name__ == "__main__":
    """
    测试代码：验证整合后的模块功能
    """
    print("=" * 80)
    print("ViTsubmodules 整合测试")
    print("=" * 80)

    # 测试参数
    batch_size = 2
    input_channels = 32
    height, width = 15, 23  # 深度图像的非正方形尺寸
    embed_dim = 384

    # 创建测试输入
    test_input = torch.randn(batch_size, input_channels, height, width)
    print(f"\n输入张量形状: {test_input.shape}")
    print(f"输入格式: [B={batch_size}, C={input_channels}, H={height}, W={width}]")

    # 测试不同的注意力机制

    # 输出使用建议
    print("\n使用建议:")
    print("1. 对于深度图像处理，推荐使用 'heat2d' 或 'heat_block' 注意力机制")
    print("2. 'heat2d' 提供更好的性能，'heat_block' 提供更完整的功能")
    print("3. 输入格式应为 [B, 32, 15, 23] 以匹配深度图像规格")
    print("4. 可以通过 resolution 参数调整空间分辨率以适配不同尺寸的输入")

    # 额外测试：对比原始MixTransformerEncoderLayer和新的HeatMixTransformerEncoderLayer
    print(f"\n{'='*80}")
    print("对比测试：原始 vs Heat2D 编码器层")
    print(f"{'='*80}")

    try:
        # 测试参数
        test_params = {
            'in_channels': input_channels,
            'out_channels': embed_dim,
            'patch_size': 3,
            'stride': 2,
            'padding': 1,
            'n_layers': 2,
            'reduction_ratio': 4,
            'num_heads': 8,
            'expansion_factor': 4
        }

        # 创建原始编码器
        print("\n1. 测试原始 MixTransformerEncoderLayer:")
        original_encoder = MixTransformerEncoderLayer(**test_params)

        with torch.no_grad():
            original_output = original_encoder(test_input)

        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {original_output.shape}")
        print(f"   ✓ 原始编码器测试成功")

        # 创建Heat2D编码器
        print("\n2. 测试新的 HeatMixTransformerEncoderLayer:")
        heat_params = test_params.copy()
        heat_params['resolution'] = (height, width)  # 添加分辨率参数

        heat_encoder = HeatMixTransformerEncoderLayer(**heat_params)

        with torch.no_grad():
            heat_output = heat_encoder(test_input)

        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {heat_output.shape}")
        print(f"   编码器配置: {heat_encoder.config}")
        print(f"   ✓ Heat2D编码器测试成功")

        # 对比输出形状
        print(f"\n3. 形状对比:")
        print(f"   原始编码器输出: {original_output.shape}")
        print(f"   Heat2D编码器输出: {heat_output.shape}")

        if original_output.shape == heat_output.shape:
            print(f"   ✓ 输出形状完全一致，接口兼容性良好")
        else:
            print(f"   ✗ 输出形状不一致，需要检查实现")

    except Exception as e:
        print(f"   ✗ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()

    print(f"\n{'='*80}")
    print("所有测试完成！")
    print(f"{'='*80}")

    print("\n总结:")
    print("1. HeatMixTransformerEncoderLayer 成功整合了 Heat2D_ViT 注意力机制")
    print("2. 保持了与原始 MixTransformerEncoderLayer 完全相同的接口")
    print("3. 支持非正方形深度图像输入 [B, 32, 15, 23]")
    print("4. 可以作为 MixTransformerEncoderLayer 的直接替代品使用")