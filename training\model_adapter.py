"""
模型适配器
用于将预训练的模仿学习模型适配到强化学习框架中

@authors: Model adaptation for RL
@organization: GRASP Lab, University of Pennsylvania
@brief: 处理模仿学习模型到强化学习模型的转换
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import copy
import sys
import os
from os.path import join as opj

# 添加模型路径
sys.path.append(opj(os.path.dirname(os.path.abspath(__file__)), '../models'))
import model as model_library

class PretrainedModelAdapter:
    """
    预训练模型适配器
    负责将模仿学习模型转换为适合强化学习的格式
    """
    
    def __init__(self, model_type='ViT'):
        """
        初始化适配器
        
        Args:
            model_type: 模型类型 ('ViT', 'ViTLSTM', etc.)
        """
        self.model_type = model_type
        self.supported_models = ['ViT', 'ViTLSTM', 'LSTMNet', 'ConvNet']
        
        if model_type not in self.supported_models:
            raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {self.supported_models}")
    
    def load_pretrained_model(self, checkpoint_path, device='cuda'):
        """
        加载预训练模型
        
        Args:
            checkpoint_path: 检查点文件路径
            device: 设备
            
        Returns:
            loaded_model: 加载的模型
        """
        # 创建模型
        if self.model_type == 'ViT':
            model = model_library.ViT().to(device).float()
        elif self.model_type == 'ViTLSTM':
            model = model_library.LSTMNetVIT().to(device).float()
        elif self.model_type == 'LSTMNet':
            model = model_library.LSTMNet().to(device).float()
        elif self.model_type == 'ConvNet':
            model = model_library.ConvNet().to(device).float()
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        # 加载权重
        try:
            state_dict = torch.load(checkpoint_path, map_location=device)
            model.load_state_dict(state_dict)
            model.eval()
            print(f"[ModelAdapter] 成功加载预训练模型: {checkpoint_path}")
        except Exception as e:
            print(f"[ModelAdapter] 加载模型失败: {e}")
            raise
        
        return model
    
    def extract_feature_extractor(self, pretrained_model):
        """
        从预训练模型中提取特征提取器（去除最后的输出层）
        
        Args:
            pretrained_model: 预训练模型
            
        Returns:
            feature_extractor: 特征提取器
            feature_dim: 特征维度
        """
        if self.model_type == 'ViT':
            return self._extract_vit_features(pretrained_model)
        elif self.model_type == 'ViTLSTM':
            return self._extract_vit_lstm_features(pretrained_model)
        elif self.model_type == 'LSTMNet':
            return self._extract_lstm_features(pretrained_model)
        elif self.model_type == 'ConvNet':
            return self._extract_conv_features(pretrained_model)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def _extract_vit_features(self, model):
        """提取ViT模型的特征"""
        # ViT模型结构：encoder_blocks -> decoder -> nn_fc1 -> nn_fc2
        # 我们保留到nn_fc1的输出，去除nn_fc2
        
        feature_extractor = nn.ModuleDict()
        
        # 复制所有模块除了最后的输出层
        for name, module in model.named_children():
            if name != 'nn_fc2':  # 排除最后的输出层
                feature_extractor[name] = copy.deepcopy(module)
        
        # 获取特征维度（nn_fc1的输出维度）
        if hasattr(model, 'nn_fc1'):
            feature_dim = model.nn_fc1.out_features
        else:
            feature_dim = 256  # 默认值
        
        return feature_extractor, feature_dim
    
    def _extract_vit_lstm_features(self, model):
        """提取ViTLSTM模型的特征"""
        # ViTLSTM模型结构：encoder_blocks -> decoder -> lstm -> nn_fc2
        # 我们保留到lstm的输出，去除nn_fc2
        
        feature_extractor = nn.ModuleDict()
        
        for name, module in model.named_children():
            if name != 'nn_fc2':  # 排除最后的输出层
                feature_extractor[name] = copy.deepcopy(module)
        
        # LSTM的输出维度
        if hasattr(model, 'lstm'):
            feature_dim = model.lstm.hidden_size
        else:
            feature_dim = 128  # 默认值
        
        return feature_extractor, feature_dim
    
    def _extract_lstm_features(self, model):
        """提取LSTM模型的特征"""
        feature_extractor = nn.ModuleDict()
        
        for name, module in model.named_children():
            if name not in ['fc3']:  # 排除最后的输出层
                feature_extractor[name] = copy.deepcopy(module)
        
        # 获取特征维度
        if hasattr(model, 'fc2'):
            feature_dim = model.fc2.out_features
        else:
            feature_dim = 16  # 默认值
        
        return feature_extractor, feature_dim
    
    def _extract_conv_features(self, model):
        """提取ConvNet模型的特征"""
        feature_extractor = nn.ModuleDict()
        
        for name, module in model.named_children():
            if name != 'fc3':  # 排除最后的输出层
                feature_extractor[name] = copy.deepcopy(module)
        
        # 获取特征维度
        if hasattr(model, 'fc2'):
            feature_dim = model.fc2.out_features
        else:
            feature_dim = 32  # 默认值
        
        return feature_extractor, feature_dim

class AdaptedActor(nn.Module):
    """
    适配的Actor网络
    使用预训练模型的特征提取器 + 新的动作输出层
    """
    
    def __init__(self, pretrained_model, model_type='ViT', action_dim=3, max_action=1.0, freeze_backbone=False):
        """
        初始化适配的Actor网络
        
        Args:
            pretrained_model: 预训练模型
            model_type: 模型类型
            action_dim: 动作维度
            max_action: 最大动作值
            freeze_backbone: 是否冻结backbone
        """
        super(AdaptedActor, self).__init__()
        
        self.model_type = model_type
        self.max_action = max_action
        self.action_dim = action_dim
        
        # 使用适配器提取特征
        adapter = PretrainedModelAdapter(model_type)
        self.feature_extractor, self.feature_dim = adapter.extract_feature_extractor(pretrained_model)
        
        # 可选择冻结backbone参数
        if freeze_backbone:
            for param in self.feature_extractor.parameters():
                param.requires_grad = False
            print(f"[AdaptedActor] Backbone参数已冻结")
        
        # 新的动作输出层
        self.action_head = nn.Sequential(
            nn.Linear(self.feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, action_dim)
        )
        
        print(f"[AdaptedActor] 创建适配Actor，特征维度: {self.feature_dim}, 动作维度: {action_dim}")
    
    def forward(self, state):
        """
        前向传播
        
        Args:
            state: 输入状态 [depth_img, desired_vel, quaternion]
            
        Returns:
            action: 输出动作
        """
        # 通过特征提取器
        features = self._extract_features(state)
        
        # 通过动作输出层
        action = self.max_action * torch.tanh(self.action_head(features))
        
        return action
    
    def _extract_features(self, state):
        """
        提取特征
        
        Args:
            state: 输入状态
            
        Returns:
            features: 提取的特征
        """
        if self.model_type == 'ViT':
            return self._extract_vit_features(state)
        elif self.model_type == 'ViTLSTM':
            return self._extract_vit_lstm_features(state)
        elif self.model_type == 'LSTMNet':
            return self._extract_lstm_features(state)
        elif self.model_type == 'ConvNet':
            return self._extract_conv_features(state)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def _extract_vit_features(self, state):
        """提取ViT特征"""
        # 复制ViT的前向传播逻辑，但不包括最后的输出层
        state = self._refine_inputs(state)
        
        x = state[0]  # 深度图像
        x = self._split_depth_map_to_layers(x)
        
        # 通过encoder blocks
        if 'encoder_blocks' in self.feature_extractor:
            embeds = [x]
            for block in self.feature_extractor.encoder_blocks:
                embeds.append(block(embeds[-1]))
            out = embeds[1:]
            
            # 特征融合
            if 'pxShuffle' in self.feature_extractor and 'up_sample' in self.feature_extractor:
                out = torch.cat([self.feature_extractor.pxShuffle(out[1]), 
                               self.feature_extractor.up_sample(out[0])], dim=1)
                if 'down_sample' in self.feature_extractor:
                    out = self.feature_extractor.down_sample(out)
            
            # 解码器
            if 'decoder' in self.feature_extractor:
                out = self.feature_extractor.decoder(out.flatten(1))
            
            # 拼接元数据
            out = torch.cat([out, state[1]/10, state[2]], dim=1).float()
            
            # 通过第一个全连接层
            if 'nn_fc1' in self.feature_extractor:
                out = F.leaky_relu(self.feature_extractor.nn_fc1(out))
            
            return out
        else:
            raise ValueError("ViT模型缺少encoder_blocks")
    
    def _extract_vit_lstm_features(self, state):
        """提取ViTLSTM特征"""
        # 类似ViT，但包括LSTM层
        features = self._extract_vit_features(state)  # 复用ViT特征提取
        
        # 通过LSTM
        if 'lstm' in self.feature_extractor:
            features, _ = self.feature_extractor.lstm(features)
        
        return features
    
    def _extract_lstm_features(self, state):
        """提取LSTM特征"""
        # 简化实现
        if isinstance(state, list):
            # 将状态转换为向量
            state_vec = torch.cat([
                state[0].flatten(1),
                state[1],
                state[2]
            ], dim=1)
        else:
            state_vec = state
        
        # 通过特征提取器
        features = state_vec
        for name, module in self.feature_extractor.items():
            if name != 'fc3':  # 排除输出层
                features = module(features)
        
        return features
    
    def _extract_conv_features(self, state):
        """提取ConvNet特征"""
        # 类似LSTM的处理
        return self._extract_lstm_features(state)
    
    def _refine_inputs(self, X):
        """输入预处理"""
        if X[2] is None:
            X[2] = torch.zeros((X[0].shape[0], 4)).float().to(X[0].device)
            X[2][:, 0] = 1

        if X[0].shape[-2] != 60 or X[0].shape[-1] != 90:
            X[0] = F.interpolate(X[0], size=(60, 90), mode='bilinear')

        return X
    
    def _split_depth_map_to_layers(self, depth_map, num_bins=6):
        """深度图分层（简化版本）"""
        if depth_map.dim() == 3:
            depth_map = depth_map.unsqueeze(1)
        
        # 简单的重复实现，实际应该使用LID方法
        layered_depth_maps = depth_map.repeat(1, num_bins, 1, 1)
        
        return layered_depth_maps
