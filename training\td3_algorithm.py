"""
TD3 (Twin Delayed Deep Deterministic Policy Gradient) Algorithm Implementation
用于无人机避障任务的强化学习算法

@authors: Based on original TD3 paper by <PERSON><PERSON> et al.
@organization: GRASP Lab, University of Pennsylvania
@brief: TD3算法实现，支持从模仿学习模型初始化Actor网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import copy
from collections import deque
import random

class ReplayBuffer:
    """经验回放缓冲区"""
    def __init__(self, capacity=1000000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """添加经验到缓冲区"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size):
        """从缓冲区采样批次数据"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)

class Actor(nn.Module):
    """Actor网络 - 基于预训练的ViT模型"""
    def __init__(self, pretrained_model=None, action_dim=3, max_action=1.0):
        super(Actor, self).__init__()
        self.max_action = max_action
        
        if pretrained_model is not None:
            # 使用预训练模型作为特征提取器
            self.feature_extractor = pretrained_model
            # 冻结预训练模型的部分参数（可选）
            # for param in self.feature_extractor.parameters():
            #     param.requires_grad = False
        else:
            # 如果没有预训练模型，创建简单的网络
            self.feature_extractor = nn.Sequential(
                nn.Linear(517, 256),  # 假设输入维度为517（图像特征+元数据）
                nn.ReLU(),
                nn.Linear(256, 128),
                nn.ReLU()
            )
        
        # 动作输出层
        self.action_head = nn.Linear(128, action_dim)
        
    def forward(self, state):
        """前向传播"""
        if hasattr(self.feature_extractor, 'forward') and len(state) > 1:
            # 如果是ViT模型，输入格式为[images, desired_vel, quaternion]
            features, _ = self.feature_extractor(state)
        else:
            # 简单网络的情况
            features = self.feature_extractor(state)
        
        action = self.max_action * torch.tanh(self.action_head(features))
        return action

class Critic(nn.Module):
    """Critic网络 - 估计Q值"""
    def __init__(self, state_dim=517, action_dim=3, hidden_dim=256):
        super(Critic, self).__init__()
        
        # Q1网络
        self.q1 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        # Q2网络
        self.q2 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, state, action):
        """前向传播，返回两个Q值"""
        # 如果state是列表格式（ViT输入），需要先提取特征
        if isinstance(state, list):
            # 这里需要一个特征提取器来处理ViT输入
            # 暂时简化处理
            state_features = torch.cat([
                state[0].flatten(1),  # 图像特征
                state[1],             # 期望速度
                state[2]              # 四元数
            ], dim=1)
        else:
            state_features = state
            
        sa = torch.cat([state_features, action], dim=1)
        q1 = self.q1(sa)
        q2 = self.q2(sa)
        return q1, q2
    
    def Q1(self, state, action):
        """只返回Q1值"""
        if isinstance(state, list):
            state_features = torch.cat([
                state[0].flatten(1),
                state[1],
                state[2]
            ], dim=1)
        else:
            state_features = state
            
        sa = torch.cat([state_features, action], dim=1)
        return self.q1(sa)

class TD3Agent:
    """TD3智能体"""
    def __init__(
        self,
        state_dim=517,
        action_dim=3,
        max_action=1.0,
        lr_actor=1e-4,
        lr_critic=1e-3,
        gamma=0.99,
        tau=0.005,
        policy_noise=0.2,
        noise_clip=0.5,
        policy_freq=2,
        device='cuda',
        pretrained_model=None
    ):
        self.device = device
        self.action_dim = action_dim
        self.max_action = max_action
        self.gamma = gamma
        self.tau = tau
        self.policy_noise = policy_noise
        self.noise_clip = noise_clip
        self.policy_freq = policy_freq
        
        # 初始化网络
        self.actor = Actor(pretrained_model, action_dim, max_action).to(device)
        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=lr_actor)
        
        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=lr_critic)
        
        self.total_it = 0
        
    def select_action(self, state, noise=0.1):
        """选择动作"""
        with torch.no_grad():
            if isinstance(state, list):
                # ViT输入格式
                state = [torch.FloatTensor(s).unsqueeze(0).to(self.device) if isinstance(s, np.ndarray) 
                        else s.unsqueeze(0).to(self.device) for s in state]
            else:
                state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            action = self.actor(state).cpu().data.numpy().flatten()
            
            if noise != 0:
                action = action + np.random.normal(0, noise, size=self.action_dim)
                action = np.clip(action, -self.max_action, self.max_action)
                
        return action
    
    def train(self, replay_buffer, batch_size=256):
        """训练智能体"""
        self.total_it += 1
        
        # 从经验回放缓冲区采样
        state, action, reward, next_state, done = replay_buffer.sample(batch_size)
        
        # 转换为张量
        state = torch.FloatTensor(state).to(self.device)
        action = torch.FloatTensor(action).to(self.device)
        reward = torch.FloatTensor(reward).to(self.device)
        next_state = torch.FloatTensor(next_state).to(self.device)
        done = torch.FloatTensor(done).to(self.device)
        
        with torch.no_grad():
            # 选择下一个动作并添加噪声
            noise = (torch.randn_like(action) * self.policy_noise).clamp(
                -self.noise_clip, self.noise_clip
            )
            next_action = (self.actor_target(next_state) + noise).clamp(
                -self.max_action, self.max_action
            )
            
            # 计算目标Q值
            target_q1, target_q2 = self.critic_target(next_state, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_q = reward + (1 - done) * self.gamma * target_q
        
        # 获取当前Q值
        current_q1, current_q2 = self.critic(state, action)
        
        # 计算Critic损失
        critic_loss = F.mse_loss(current_q1, target_q) + F.mse_loss(current_q2, target_q)
        
        # 优化Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()
        
        # 延迟策略更新
        if self.total_it % self.policy_freq == 0:
            # 计算Actor损失
            actor_loss = -self.critic.Q1(state, self.actor(state)).mean()
            
            # 优化Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
            
            # 软更新目标网络
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
            
            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
    
    def save(self, filename):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic': self.critic.state_dict(),
            'actor_target': self.actor_target.state_dict(),
            'critic_target': self.critic_target.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic_optimizer': self.critic_optimizer.state_dict(),
        }, filename)
    
    def load(self, filename):
        """加载模型"""
        checkpoint = torch.load(filename, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic.load_state_dict(checkpoint['critic'])
        self.actor_target.load_state_dict(checkpoint['actor_target'])
        self.critic_target.load_state_dict(checkpoint['critic_target'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer'])
