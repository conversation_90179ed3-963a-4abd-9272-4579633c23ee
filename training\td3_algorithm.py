"""
TD3 (Twin Delayed Deep Deterministic Policy Gradient) Algorithm Implementation
用于无人机避障任务的强化学习算法

@authors: Based on original TD3 paper by <PERSON><PERSON> et al.
@organization: GRASP Lab, University of Pennsylvania
@brief: TD3算法实现，支持从模仿学习模型初始化Actor网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import copy
from collections import deque
import random

# 导入模型适配器
from model_adapter import AdaptedActor

class ReplayBuffer:
    """经验回放缓冲区"""
    def __init__(self, capacity=1000000):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        """添加经验到缓冲区"""
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        """从缓冲区采样批次数据"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done

    def __len__(self):
        return len(self.buffer)

class Actor(nn.Module):
    """Actor网络 - 基于预训练的ViT模型"""
    def __init__(self, pretrained_model=None, action_dim=3, max_action=1.0, freeze_backbone=False):
        super(Actor, self).__init__()
        self.max_action = max_action
        self.action_dim = action_dim

        if pretrained_model is not None:
            # 使用预训练模型作为特征提取器
            self.use_pretrained = True
            self.backbone = self._extract_backbone(pretrained_model)

            # 可选择冻结backbone参数
            if freeze_backbone:
                for param in self.backbone.parameters():
                    param.requires_grad = False

            # 获取backbone输出维度
            self.feature_dim = self._get_backbone_output_dim(pretrained_model)

        else:
            # 如果没有预训练模型，创建简单的网络
            self.use_pretrained = False
            self.backbone = nn.Sequential(
                nn.Linear(517, 256),  # 假设输入维度为517（图像特征+元数据）
                nn.ReLU(),
                nn.Linear(256, 128),
                nn.ReLU()
            )
            self.feature_dim = 128

        # 新的动作输出层（替换预训练模型的最后一层）
        self.action_head = nn.Sequential(
            nn.Linear(self.feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim)
        )

    def _extract_backbone(self, pretrained_model):
        """从预训练模型中提取backbone（去除最后的输出层）"""
        if hasattr(pretrained_model, 'nn_fc2'):
            # ViT模型：移除最后的输出层nn_fc2
            backbone_modules = []
            for name, module in pretrained_model.named_children():
                if name != 'nn_fc2':  # 排除最后的输出层
                    backbone_modules.append((name, module))

            # 创建新的backbone
            backbone = nn.ModuleDict(backbone_modules)
            return backbone
        else:
            # 其他模型类型的处理
            return pretrained_model

    def _get_backbone_output_dim(self, pretrained_model):
        """获取backbone的输出维度"""
        if hasattr(pretrained_model, 'nn_fc1'):
            # ViT模型：nn_fc1的输出维度
            return pretrained_model.nn_fc1.out_features
        else:
            # 默认维度
            return 256

    def _forward_backbone(self, state):
        """通过backbone前向传播"""
        if self.use_pretrained:
            # 复制ViT的前向传播逻辑，但不包括最后的输出层
            state = self._refine_inputs(state)

            x = state[0]  # 深度图像
            x = self._split_depth_map_to_layers(x)

            # 通过encoder blocks
            if hasattr(self.backbone, 'encoder_blocks'):
                embeds = [x]
                for block in self.backbone.encoder_blocks:
                    embeds.append(block(embeds[-1]))
                out = embeds[1:]

                # 特征融合
                if hasattr(self.backbone, 'pxShuffle') and hasattr(self.backbone, 'up_sample'):
                    out = torch.cat([self.backbone.pxShuffle(out[1]), self.backbone.up_sample(out[0])], dim=1)
                    if hasattr(self.backbone, 'down_sample'):
                        out = self.backbone.down_sample(out)

                # 解码器
                if hasattr(self.backbone, 'decoder'):
                    out = self.backbone.decoder(out.flatten(1))

                # 拼接元数据
                out = torch.cat([out, state[1]/10, state[2]], dim=1).float()

                # 通过第一个全连接层
                if hasattr(self.backbone, 'nn_fc1'):
                    out = F.leaky_relu(self.backbone.nn_fc1(out))

                return out
            else:
                # 简化处理
                return self.backbone(state)
        else:
            # 简单网络
            return self.backbone(state)

    def _refine_inputs(self, X):
        """输入预处理（复制自models/model.py）"""
        # fill quaternion rotation if not given
        if X[2] is None:
            X[2] = torch.zeros((X[0].shape[0], 4)).float().to(X[0].device)
            X[2][:, 0] = 1

        # if input depth images are not of right shape, resize
        if X[0].shape[-2] != 60 or X[0].shape[-1] != 90:
            X[0] = F.interpolate(X[0], size=(60, 90), mode='bilinear')

        return X

    def _split_depth_map_to_layers(self, depth_map, depth_min=0.0, depth_max=1.0, num_bins=6, mask_value=-1.0):
        """深度图分层（复制自models/model.py）"""
        # 确保深度图至少是3维的 [B, H, W]
        if depth_map.dim() == 2:
            depth_map = depth_map.unsqueeze(0)  # 添加批次维度

        B, C, H, W = depth_map.shape

        # 计算每个像素属于哪一层
        bin_indices = self._depth_to_bins_lid(depth_map, depth_min, depth_max, num_bins)
        bin_indices = bin_indices.long()  # 转换为整数索引

        # 创建输出张量，形状为 [B, num_bins, H, W]
        layered_depth_maps = torch.full((B, num_bins, H, W), mask_value, device=depth_map.device)

        # 为每一层创建掩码并填充深度值
        for b in range(B):
            for n in range(num_bins):
                # 创建当前层的掩码
                mask = (bin_indices[b] == n)

                # 将原始深度值填充到对应层的对应位置
                layered_depth_maps[b, n] = torch.where(mask, depth_map[b], torch.tensor(mask_value, device=depth_map.device))

        # 如果输入是2D的，则移除批次维度
        if depth_map.shape[0] == 1 and len(depth_map.shape) == 3:
            layered_depth_maps = layered_depth_maps.squeeze(0)

        return layered_depth_maps

    def _depth_to_bins_lid(self, depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
        """将深度图转换为深度分层索引，使用LID方法"""
        # 计算分割点
        split_points = []
        for i in range(1, num_bins):
            t = i / num_bins
            boundary = 1 - t ** 1.5
            split_points.append(boundary)

        # 构建完整的边界列表：[0, boundary1, boundary2, ..., boundaryN, 1]
        boundaries = [0.0] + split_points + [1.0]

        # 转换为张量，保持与输入相同的设备和数据类型
        boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)

        # 处理边界情况：将超出[0,1]范围的值裁剪到有效范围
        depth_map_clipped = torch.clamp(depth_map, depth_min, depth_max)

        # 使用 torch.searchsorted 进行高效的区间查找
        indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)

        # 确保索引在有效范围内 [0, num_bins]
        indices = torch.clamp(indices, 0, num_bins-1)

        return indices

    def forward(self, state):
        """前向传播"""
        # 通过backbone提取特征
        features = self._forward_backbone(state)

        # 通过新的动作输出层
        action = self.max_action * torch.tanh(self.action_head(features))
        return action

class Critic(nn.Module):
    """Critic网络 - 估计Q值"""
    def __init__(self, state_dim=517, action_dim=3, hidden_dim=256):
        super(Critic, self).__init__()

        # Q1网络
        self.q1 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

        # Q2网络
        self.q2 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

    def forward(self, state, action):
        """前向传播，返回两个Q值"""
        # 如果state是列表格式（ViT输入），需要先提取特征
        if isinstance(state, list):
            # 这里需要一个特征提取器来处理ViT输入
            # 暂时简化处理
            state_features = torch.cat([
                state[0].flatten(1),  # 图像特征
                state[1],             # 期望速度
                state[2]              # 四元数
            ], dim=1)
        else:
            state_features = state

        sa = torch.cat([state_features, action], dim=1)
        q1 = self.q1(sa)
        q2 = self.q2(sa)
        return q1, q2

    def Q1(self, state, action):
        """只返回Q1值"""
        if isinstance(state, list):
            state_features = torch.cat([
                state[0].flatten(1),
                state[1],
                state[2]
            ], dim=1)
        else:
            state_features = state

        sa = torch.cat([state_features, action], dim=1)
        return self.q1(sa)

class TD3Agent:
    """TD3智能体"""
    def __init__(
        self,
        state_dim=517,
        action_dim=3,
        max_action=1.0,
        lr_actor=1e-4,
        lr_critic=1e-3,
        gamma=0.99,
        tau=0.005,
        policy_noise=0.2,
        noise_clip=0.5,
        policy_freq=2,
        device='cuda',
        pretrained_model=None,
        model_type='ViT',
        freeze_backbone=False
    ):
        self.device = device
        self.action_dim = action_dim
        self.max_action = max_action
        self.gamma = gamma
        self.tau = tau
        self.policy_noise = policy_noise
        self.noise_clip = noise_clip
        self.policy_freq = policy_freq
        self.model_type = model_type

        # 初始化Actor网络
        if pretrained_model is not None:
            # 使用适配的Actor网络
            self.actor = AdaptedActor(
                pretrained_model=pretrained_model,
                model_type=model_type,
                action_dim=action_dim,
                max_action=max_action,
                freeze_backbone=freeze_backbone
            ).to(device)
        else:
            # 使用原始Actor网络
            self.actor = Actor(None, action_dim, max_action).to(device)

        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=lr_actor)

        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=lr_critic)

        self.total_it = 0

    def select_action(self, state, noise=0.1):
        """选择动作"""
        with torch.no_grad():
            if isinstance(state, list):
                # ViT输入格式
                state = [torch.FloatTensor(s).unsqueeze(0).to(self.device) if isinstance(s, np.ndarray)
                        else s.unsqueeze(0).to(self.device) for s in state]
            else:
                state = torch.FloatTensor(state).unsqueeze(0).to(self.device)

            action = self.actor(state).cpu().data.numpy().flatten()

            if noise != 0:
                action = action + np.random.normal(0, noise, size=self.action_dim)
                action = np.clip(action, -self.max_action, self.max_action)

        return action

    def train(self, replay_buffer, batch_size=256):
        """训练智能体"""
        self.total_it += 1

        # 从经验回放缓冲区采样
        state, action, reward, next_state, done = replay_buffer.sample(batch_size)

        # 转换为张量
        state = torch.FloatTensor(state).to(self.device)
        action = torch.FloatTensor(action).to(self.device)
        reward = torch.FloatTensor(reward).to(self.device)
        next_state = torch.FloatTensor(next_state).to(self.device)
        done = torch.FloatTensor(done).to(self.device)

        with torch.no_grad():
            # 选择下一个动作并添加噪声
            noise = (torch.randn_like(action) * self.policy_noise).clamp(
                -self.noise_clip, self.noise_clip
            )
            next_action = (self.actor_target(next_state) + noise).clamp(
                -self.max_action, self.max_action
            )

            # 计算目标Q值
            target_q1, target_q2 = self.critic_target(next_state, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_q = reward + (1 - done) * self.gamma * target_q

        # 获取当前Q值
        current_q1, current_q2 = self.critic(state, action)

        # 计算Critic损失
        critic_loss = F.mse_loss(current_q1, target_q) + F.mse_loss(current_q2, target_q)

        # 优化Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

        # 延迟策略更新
        if self.total_it % self.policy_freq == 0:
            # 计算Actor损失
            actor_loss = -self.critic.Q1(state, self.actor(state)).mean()

            # 优化Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()

            # 软更新目标网络
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def save(self, filename):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic': self.critic.state_dict(),
            'actor_target': self.actor_target.state_dict(),
            'critic_target': self.critic_target.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic_optimizer': self.critic_optimizer.state_dict(),
        }, filename)

    def load(self, filename):
        """加载模型"""
        checkpoint = torch.load(filename, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic.load_state_dict(checkpoint['critic'])
        self.actor_target.load_state_dict(checkpoint['actor_target'])
        self.critic_target.load_state_dict(checkpoint['critic_target'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer'])
