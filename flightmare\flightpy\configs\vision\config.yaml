environment:
  level: "spheres_medium" # provided: spheres_medium or trees
  env_folder: "environment_0" # environment_<0-100> for spheres_medium, environment_<0-100> for trees. no leading zeros.
  world_box: [-20, 80, -10, 10, 0.0, 15] # Bounding box applied during training, exiting this box terminates the episode. [xmin, xmax, ymin, ymax, zmin, zmax]
  goal_vel: [1.0, 0.0, 0.0] # goal velicty for tracking vx, vy, vz
  max_detection_range: 10.0 # max obstacle detection range [m], obstacles outside this range will not be detected. The number of obstacles detected is fixed to 10, if less than 10 obstacles are within detectable range, the observation is padded with 10.0

datagen: 0
rollout: 1

rewards: # unused
  vel_coeff: -0.01
  collision_coeff: -0.01
  angular_vel_coeff: -0.0001
  survive_rew: 0.03
  names:
    [
      "lin_vel_reward",
      "collision_penalty",
      "ang_vel_penalty",
      "survive_rew",
      "total",
    ]

rgb_camera:
  on: yes 
  t_BC: [0.3, 0.0, 0.0] # translational vector of the camera with repect to the body frame
  r_BC: [0.0, 0.0, -90] # rotational angle (roll, pitch, yaw) of the camera in degree.
  channels: 3
  width: 640 
  height: 480
  fov: 100.0
  enable_depth: yes 
  enable_segmentation: no
  enable_opticalflow: no

unity:
  scene_id: 2 # 0 empty, 1 industrial, 2 warehouse
  render: no

simulation:
  seed: 10
  sim_dt: 0.01
  max_t: 400.0
  num_envs: 100 
  num_threads: 200

quadrotor_dynamics:
  mass: 0.752 #0.752
  tbm_fr: [0.075, -0.10, 0.0] # [m]
  tbm_bl: [-0.075, 0.10, 0.0] # [m]
  tbm_br: [-0.075, -0.10, 0.0] # [m]
  tbm_fl: [0.075, 0.10, 0.0] # [m
  omega_max: [6.0, 6.0, 2.0]
  inertia:  [0.0025, 0.0021, 0.0043]
  kappa: 0.022
  motor_omega_min: 0.0
  motor_omega_max: 2800.0
  motor_tau: 0.033
  thrust_map: [1.562522e-6, 0.0, 0.0] # max thrust = 8.50 N
  body_drag_1: [0.00, 0.00, 0.00]
  body_drag_3: [0.00, 0.00, 0.00]
  body_drag_h: 0.00
