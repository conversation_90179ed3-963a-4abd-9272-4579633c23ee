"""
两阶段训练测试脚本
用于验证TD3算法和环境包装器的基本功能

@authors: Test script for two-stage training
@organization: GRASP Lab, University of Pennsylvania
@brief: 测试脚本，验证各个组件的功能
"""

import torch
import numpy as np
import sys
import os
from os.path import join as opj

# 添加路径
sys.path.append(opj(os.path.dirname(os.path.abspath(__file__)), '../models'))

# 导入测试模块
from td3_algorithm import TD3Agent, Replay<PERSON>uffer, Actor, Critic
from rl_env_wrapper import FlightmareEnvWrapper
from model_adapter import PretrainedModelAdapter, AdaptedActor
import model as model_library

def test_td3_components():
    """测试TD3算法组件"""
    print("=" * 50)
    print("测试TD3算法组件")
    print("=" * 50)

    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 测试ReplayBuffer
    print("\n1. 测试ReplayBuffer...")
    buffer = ReplayBuffer(capacity=1000)

    # 添加一些虚拟经验
    for i in range(10):
        state = np.random.random(517)
        action = np.random.random(3)
        reward = np.random.random()
        next_state = np.random.random(517)
        done = False
        buffer.push(state, action, reward, next_state, done)

    print(f"缓冲区大小: {len(buffer)}")

    # 测试采样
    if len(buffer) >= 5:
        batch = buffer.sample(5)
        print(f"采样批次形状: {[b.shape for b in batch]}")

    # 测试Actor网络
    print("\n2. 测试Actor网络...")
    actor = Actor(action_dim=3, max_action=1.0).to(device)

    # 测试简单输入
    test_input = torch.randn(1, 517).to(device)
    action = actor(test_input)
    print(f"Actor输出形状: {action.shape}")
    print(f"动作范围: [{action.min().item():.3f}, {action.max().item():.3f}]")

    # 测试Critic网络
    print("\n3. 测试Critic网络...")
    critic = Critic(state_dim=517, action_dim=3).to(device)

    test_state = torch.randn(1, 517).to(device)
    test_action = torch.randn(1, 3).to(device)
    q1, q2 = critic(test_state, test_action)
    print(f"Critic输出形状: Q1={q1.shape}, Q2={q2.shape}")

    # 测试TD3Agent
    print("\n4. 测试TD3Agent...")
    agent = TD3Agent(
        state_dim=517,
        action_dim=3,
        device=device
    )

    # 测试动作选择
    test_state_np = np.random.random(517)
    action = agent.select_action(test_state_np)
    print(f"智能体选择的动作: {action}")

    print("TD3组件测试完成!")

def test_model_adapter():
    """测试模型适配器"""
    print("\n" + "=" * 50)
    print("测试模型适配器")
    print("=" * 50)

    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    try:
        # 创建ViT模型
        print("1. 创建ViT模型...")
        vit_model = model_library.ViT().to(device).float()

        # 测试模型适配器
        print("2. 测试模型适配器...")
        adapter = PretrainedModelAdapter('ViT')
        feature_extractor, feature_dim = adapter.extract_feature_extractor(vit_model)
        print(f"特征维度: {feature_dim}")

        # 测试适配的Actor
        print("3. 测试适配的Actor...")
        adapted_actor = AdaptedActor(
            pretrained_model=vit_model,
            model_type='ViT',
            action_dim=3,
            max_action=1.0
        ).to(device)

        # 测试输入
        depth_img = torch.randn(1, 1, 60, 90).to(device)
        desired_vel = torch.randn(1, 1).to(device)
        quaternion = torch.randn(1, 4).to(device)

        vit_input = [depth_img, desired_vel, quaternion]

        action = adapted_actor(vit_input)
        print(f"适配Actor输出形状: {action.shape}")
        print(f"动作值: {action.detach().cpu().numpy()}")
        print(f"动作范围: [{action.min().item():.3f}, {action.max().item():.3f}]")

        # 测试与TD3的集成
        print("4. 测试与TD3的完整集成...")
        agent = TD3Agent(
            state_dim=517,
            action_dim=3,
            device=device,
            pretrained_model=vit_model,
            model_type='ViT'
        )

        action = agent.select_action(vit_input)
        print(f"TD3智能体动作: {action}")

    except Exception as e:
        print(f"模型适配器测试失败: {e}")
        import traceback
        traceback.print_exc()

    print("模型适配器测试完成!")

def test_vit_integration():
    """测试ViT模型集成（保留原有测试）"""
    print("\n" + "=" * 50)
    print("测试ViT模型集成")
    print("=" * 50)

    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建ViT模型
    print("1. 创建ViT模型...")
    vit_model = model_library.ViT().to(device).float()

    # 测试ViT输入格式
    print("2. 测试ViT输入格式...")
    depth_img = torch.randn(1, 1, 60, 90).to(device)
    desired_vel = torch.randn(1, 1).to(device)
    quaternion = torch.randn(1, 4).to(device)

    vit_input = [depth_img, desired_vel, quaternion]

    try:
        output, _ = vit_model(vit_input)
        print(f"ViT输出形状: {output.shape}")

        # 测试与TD3的集成
        print("3. 测试ViT与TD3集成...")
        actor_with_vit = Actor(pretrained_model=vit_model, action_dim=3).to(device)

        action = actor_with_vit(vit_input)
        print(f"集成后的动作输出形状: {action.shape}")
        print(f"动作值: {action.detach().cpu().numpy()}")

    except Exception as e:
        print(f"ViT集成测试失败: {e}")
        import traceback
        traceback.print_exc()

    print("ViT集成测试完成!")

def test_environment_wrapper():
    """测试环境包装器"""
    print("\n" + "=" * 50)
    print("测试环境包装器")
    print("=" * 50)

    try:
        # 创建环境（使用虚拟模式）
        print("1. 创建环境包装器...")
        env = FlightmareEnvWrapper()

        # 测试重置
        print("2. 测试环境重置...")
        obs = env.reset()
        print(f"初始观测类型: {type(obs)}")
        if isinstance(obs, list):
            print(f"观测组件形状: {[o.shape for o in obs]}")

        # 测试步进
        print("3. 测试环境步进...")
        action = np.array([0.5, 0.0, 0.0])  # 向前飞行

        next_obs, reward, done, info = env.step(action)
        print(f"奖励: {reward:.3f}")
        print(f"完成: {done}")
        print(f"信息: {info}")

        # 测试多步
        print("4. 测试多步交互...")
        total_reward = 0
        for step in range(5):
            action = np.random.uniform(-0.5, 0.5, 3)
            obs, reward, done, info = env.step(action)
            total_reward += reward
            print(f"步骤 {step+1}: 奖励={reward:.3f}, 累计奖励={total_reward:.3f}")

            if done:
                print("环境提前结束")
                break

        env.close()

    except Exception as e:
        print(f"环境测试失败: {e}")
        import traceback
        traceback.print_exc()

    print("环境包装器测试完成!")

def test_full_integration():
    """测试完整集成"""
    print("\n" + "=" * 50)
    print("测试完整集成")
    print("=" * 50)

    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    try:
        # 创建环境
        env = FlightmareEnvWrapper()

        # 创建ViT模型
        vit_model = model_library.ViT().to(device).float()

        # 创建TD3智能体
        agent = TD3Agent(
            state_dim=517,
            action_dim=3,
            device=device,
            pretrained_model=vit_model
        )

        # 创建经验回放缓冲区
        replay_buffer = ReplayBuffer(capacity=1000)

        print("1. 运行简短的训练循环...")

        # 收集一些经验
        state = env.reset()
        for step in range(10):
            action = agent.select_action(state, noise=0.3)
            next_state, reward, done, info = env.step(action)

            # 转换状态格式用于存储
            if isinstance(state, list):
                state_np = np.concatenate([
                    state[0].cpu().numpy().flatten(),
                    state[1].cpu().numpy(),
                    state[2].cpu().numpy()
                ])
                next_state_np = np.concatenate([
                    next_state[0].cpu().numpy().flatten(),
                    next_state[1].cpu().numpy(),
                    next_state[2].cpu().numpy()
                ])
            else:
                state_np = state
                next_state_np = next_state

            replay_buffer.push(state_np, action, reward, next_state_np, float(done))

            print(f"步骤 {step+1}: 动作={action}, 奖励={reward:.3f}")

            state = next_state
            if done:
                state = env.reset()

        # 测试训练
        if len(replay_buffer) >= 5:
            print("2. 测试智能体训练...")
            agent.train(replay_buffer, batch_size=5)
            print("训练步骤完成!")

        env.close()

    except Exception as e:
        print(f"完整集成测试失败: {e}")
        import traceback
        traceback.print_exc()

    print("完整集成测试完成!")

def main():
    """主测试函数"""
    print("开始两阶段训练系统测试")
    print("=" * 60)

    # 运行各项测试
    test_td3_components()
    test_model_adapter()  # 新增的模型适配器测试
    test_vit_integration()
    test_environment_wrapper()
    test_full_integration()

    print("\n" + "=" * 60)
    print("所有测试完成!")

if __name__ == '__main__':
    main()
